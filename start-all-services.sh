#!/bin/bash

# 启动所有Docker服务的脚本
# 分别启动models和records服务组

set -e

echo "=== 大语言模型管理系统 - 服务启动脚本 ==="
echo ""

# 启动模型服务组
echo "=== 1. 启动模型服务组 ==="
cd models
./start.sh
cd ..
echo ""

# 启动记录服务组
echo "=== 2. 启动记录服务组 ==="
cd records
./start.sh
cd ..
echo ""

echo "=== 所有服务启动完成 ==="
echo ""
echo "服务组织结构:"
echo "  models/     - 模型服务组 (3个镜像: models_db, model-manager, model-api-service)"
echo "  records/    - 记录服务组 (2个镜像: records_db, record-api-service)"
echo ""
echo "常用命令:"
echo "  停止所有服务: ./stop-all-services.sh"
echo "  启动模型服务: cd models && ./start.sh"
echo "  启动记录服务: cd records && ./start.sh"
echo "  停止模型服务: cd models && ./stop.sh"
echo "  停止记录服务: cd records && ./stop.sh"
