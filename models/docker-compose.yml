services:
  # 模型数据库
  models_db:
    image: mysql:8.0
    container_name: models_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: models_root_password
      MYSQL_DATABASE: vdb_models
      MYSQL_USER: models_user
      MYSQL_PASSWORD: models_password
    ports:
      - "3306:3306"
    volumes:
      - /vdb_models:/var/lib/mysql
      - ./models_database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./models_database/backup:/backup
    networks:
      - models_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "models_user", "-pmodels_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 模型管理服务
  model-manager:
    build:
      context: ./model_manager
      dockerfile: Dockerfile
    container_name: model-manager
    restart: unless-stopped
    ports:
      - "20000:20000"
    env_file:
      - ./model_manager/.env
    environment:
      FLASK_ENV: production
      PYTHONUNBUFFERED: 1
      SERVICE_TYPE: model-manager
    volumes:
      - model_logs:/app/logs
      - model_uploads:/app/uploads
    networks:
      - models_network
    depends_on:
      models_db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:20000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 模型API服务
  model-api-service:
    build:
      context: ./model_api_service
      dockerfile: Dockerfile
    container_name: model-api-service
    restart: unless-stopped
    ports:
      - "6001:6001"
    env_file:
      - ./model_api_service/.env
    environment:
      FLASK_ENV: production
      PYTHONUNBUFFERED: 1
      SERVICE_TYPE: model-api-service
    volumes:
      - model_api_logs:/app/logs
      - model_api_uploads:/app/uploads
    networks:
      - models_network
    depends_on:
      models_db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  models_network:
    name: models_network
    driver: bridge

volumes:
  model_logs:
    driver: local
  model_uploads:
    driver: local
  model_api_logs:
    driver: local
  model_api_uploads:
    driver: local
  models_data:
    driver: local
