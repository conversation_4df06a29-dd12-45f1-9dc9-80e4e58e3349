#!/bin/bash

# 启动模型服务组 (3个镜像: models_db, model-manager, model-api-service)

set -e

echo "=== 启动模型服务组 ==="
echo ""

# 检查Docker和Docker Compose是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装或未在PATH中"
    exit 1
fi

if ! docker compose version &> /dev/null; then
    echo "错误: Docker Compose 未安装或未在PATH中"
    exit 1
fi

# 检查模型数据目录是否存在，如果不存在则创建
echo "检查模型数据目录..."
if [ ! -d "/vdb_models" ]; then
    echo "创建模型数据库目录: /vdb_models"
    sudo mkdir -p /vdb_models
    sudo chown -R 999:999 /vdb_models
fi
echo "模型数据目录检查完成"
echo ""

# 启动模型服务组
echo "启动模型服务组 (数据库 + 管理服务 + API服务)..."
docker compose up -d --build

echo ""
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "=== 模型服务组状态 ==="
docker compose ps
echo ""

# 显示服务访问信息
echo "=== 服务访问信息 ==="
echo "  - 模型数据库: localhost:3306 (用户: models_user)"
echo "  - 模型管理服务: http://localhost:20000"
echo "  - 模型API服务: http://localhost:6001"
echo ""

# 健康检查
echo "=== 健康检查 ==="
echo "等待服务完全启动..."
sleep 30

echo "检查模型管理服务健康状态..."
curl -f http://localhost:20000/ 2>/dev/null && echo "✓ 模型管理服务正常" || echo "✗ 模型管理服务异常"

echo "检查模型API服务健康状态..."
curl -f http://localhost:6001/api/v1/health 2>/dev/null && echo "✓ 模型API服务正常" || echo "✗ 模型API服务异常"

echo ""
echo "=== 模型服务组启动完成 ==="
echo ""
echo "常用命令:"
echo "  查看服务状态: docker compose ps"
echo "  停止服务: ./stop.sh"
echo "  查看日志: docker compose logs -f"
echo "  查看特定服务日志: docker compose logs -f [service_name]"
