# 大语言模型数据库服务

这是一个独立的MySQL数据库服务，专门用于存储大语言模型相关的信息。

## 功能

- 存储AI平台信息
- 存储AI模型配置和价格
- 存储应用信息
- 管理应用与模型的关联关系

## 数据库信息

- **数据库名**: vdb_models
- **端口**: 3306
- **用户**: models_user
- **密码**: models_password
- **数据盘**: /vdb_models

## 包含的表

- `platforms` - AI平台管理（如OpenAI、Claude等）
- `ai_models` - AI模型信息和价格配置
- `applications` - 应用管理和API密钥
- `app_models` - 应用与模型的关联关系
- `system_info` - 系统版本信息

## 启动服务

```bash
# 创建数据目录
sudo mkdir -p /vdb_models
sudo chown -R 999:999 /vdb_models

# 启动数据库服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 连接数据库

```bash
# 使用Docker连接
docker-compose exec models-db mysql -u models_user -p

# 使用外部客户端连接
mysql -h localhost -P 3306 -u models_user -p vdb_models
```

## 停止服务

```bash
docker-compose down
```

## 备份数据

```bash
# 创建备份目录
mkdir -p backup

# 备份数据库
docker-compose exec models-db mysqldump -u models_user -p vdb_models > backup/models_backup_$(date +%Y%m%d_%H%M%S).sql
```
