{% extends "base.html" %}

{% block title %}模型管理 - 大语言模型管理系统{% endblock %}

{% block header %}模型管理{% endblock %}

{% block content %}
<div class="d-flex justify-content-between mb-3">
    <h3>模型列表</h3>
    <a href="{{ url_for('main.add_model') }}" class="btn btn-primary">
        <i class="bi bi-plus"></i> 添加模型
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>显示名称</th>
                        <th>内部名称</th>
                        <th>平台</th>
                        <th>输入价格</th>
                        <th>输出价格</th>
                        <th>图片价格</th>
                        <th>可见</th>
                        <th>免费</th>
                        <th>高价</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for model in models %}
                    <tr>
                        <td>{{ model.id }}</td>
                        <td>{{ model.display_name }}</td>
                        <td>{{ model.internal_name }}</td>
                        <td>{{ model.platform.name }}</td>
                        <td>${{ "%.6f"|format(model.input_token_price) }}</td>
                        <td>${{ "%.6f"|format(model.output_token_price) }}</td>
                        <td>${{ "%.6f"|format(model.input_picture_price) }}</td>
                        <td>
                            {% if model.is_visible_model %}
                            <span class="badge bg-success">是</span>
                            {% else %}
                            <span class="badge bg-secondary">否</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if model.free %}
                            <span class="badge bg-success">是</span>
                            {% else %}
                            <span class="badge bg-secondary">否</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if model.high_price %}
                            <span class="badge bg-danger">是</span>
                            {% else %}
                            <span class="badge bg-secondary">否</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('main.edit_model', id=model.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-pencil"></i> 编辑
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                    data-item-id="{{ model.id }}"
                                    data-item-name="{{ model.display_name }}"
                                    data-delete-url="{{ url_for('main.delete_model', id=model.id) }}">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="11" class="text-center">暂无模型数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 通用删除确认对话框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteMessage">确定要删除此项目吗？此操作不可逆。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 处理删除按钮点击事件
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    const deleteForm = document.getElementById('deleteForm');
    const deleteMessage = document.getElementById('deleteMessage');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.getAttribute('data-item-id');
            const itemName = this.getAttribute('data-item-name');
            const deleteUrl = this.getAttribute('data-delete-url');
            
            // 更新模态框内容
            deleteMessage.textContent = `确定要删除模型 "${itemName}" 吗？此操作不可逆。`;
            deleteForm.action = deleteUrl;
            
            // 显示模态框
            deleteModal.show();
        });
    });
});
</script>
{% endblock %}
