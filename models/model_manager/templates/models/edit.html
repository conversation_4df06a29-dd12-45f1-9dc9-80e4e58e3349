{% extends "base.html" %}

{% block title %}编辑模型 - 大语言模型管理系统{% endblock %}

{% block header %}编辑模型{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.edit_model', id=model.id) }}">
            {{ form.hidden_tag() }}
            <div class="mb-3">
                <label for="display_name" class="form-label">显示名称</label>
                <input type="text" class="form-control" id="display_name" name="display_name"
                       value="{{ model.display_name }}" required>
                <div class="form-text">模型的显示名称，如 GPT-4、<PERSON> 等</div>
            </div>

            <div class="mb-3">
                <label for="internal_name" class="form-label">内部名称</label>
                <input type="text" class="form-control" id="internal_name" name="internal_name" 
                       value="{{ model.internal_name }}" required>
                <div class="form-text">模型的内部标识，如 gpt-4、claude-2 等</div>
            </div>

            <div class="mb-3">
                <label for="platform_id" class="form-label">所属平台</label>
                <select class="form-select" id="platform_id" name="platform_id" required>
                    <option value="">-- 选择平台 --</option>
                    {% for platform in platforms %}
                    <option value="{{ platform.id }}" {% if platform.id == model.platform_id %}selected{% endif %}>
                        {{ platform.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="mb-3">
                <label for="input_token_price" class="form-label">输入Token价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="input_token_price" name="input_token_price" 
                           step="0.000001" min="0" value="{{ "%.6f"|format(model.input_token_price) }}" required>
                    <span class="input-group-text">/ 1000 tokens</span>
                </div>
                <div class="form-text">每1000个输入token的价格（美元）</div>
            </div>

            <div class="mb-3">
                <label for="output_token_price" class="form-label">输出Token价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="output_token_price" name="output_token_price" 
                           step="0.000001" min="0" value="{{ "%.6f"|format(model.output_token_price) }}" required>
                    <span class="input-group-text">/ 1000 tokens</span>
                </div>
                <div class="form-text">每1000个输出token的价格（美元）</div>
            </div>

            <div class="mb-3">
                <label for="input_picture_price" class="form-label">输入图片价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="input_picture_price" name="input_picture_price" 
                           step="0.000001" min="0" value="{{ "%.6f"|format(model.input_picture_price) }}" required>
                    <span class="input-group-text">/ 1000 tokens</span>
                </div>
                <div class="form-text">每1000个图片token的价格（美元）</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="is_visible_model" name="is_visible_model"
                       {% if model.is_visible_model %}checked{% endif %}>
                <label class="form-check-label" for="is_visible_model">可见模型</label>
                <div class="form-text">是否在用户界面中显示此模型</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="free" name="free"
                       {% if model.free %}checked{% endif %}>
                <label class="form-check-label" for="free">免费模型</label>
                <div class="form-text">是否为免费模型（不计费）</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="high_price" name="high_price"
                       {% if model.high_price %}checked{% endif %}>
                <label class="form-check-label" for="high_price">高价模型</label>
                <div class="form-text">是否为高价模型（需要特殊权限）</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_models') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">更新</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // No-op
});
</script>
{% endblock %}
