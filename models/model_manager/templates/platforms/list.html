{% extends "base.html" %}

{% block title %}平台管理 - 大语言模型管理系统{% endblock %}

{% block header %}平台管理{% endblock %}

{% block content %}
<div class="d-flex justify-content-between mb-3">
    <h3>平台列表</h3>
    <div>
        <a href="{{ url_for('main.init_platforms') }}" class="btn btn-outline-info me-2">
            <i class="bi bi-arrow-clockwise"></i> 平台初始化
        </a>
        <a href="{{ url_for('main.add_platform') }}" class="btn btn-primary">
            <i class="bi bi-plus"></i> 添加平台
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>平台名称</th>
                        <th>Base URL</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for platform in platforms %}
                    <tr>
                        <td>{{ platform.id }}</td>
                        <td>{{ platform.name }}</td>
                        <td>{{ platform.base_url }}</td>
                        <td>{{ platform.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        <td>
                            <a href="{{ url_for('main.edit_platform', id=platform.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-pencil"></i> 编辑
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                    data-item-id="{{ platform.id }}"
                                    data-item-name="{{ platform.name }}"
                                    data-delete-url="{{ url_for('main.delete_platform', id=platform.id) }}">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="5" class="text-center">暂无平台数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 通用删除确认对话框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteMessage">确定要删除此项目吗？此操作不可逆。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 处理删除按钮点击事件
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    const deleteForm = document.getElementById('deleteForm');
    const deleteMessage = document.getElementById('deleteMessage');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.getAttribute('data-item-id');
            const itemName = this.getAttribute('data-item-name');
            const deleteUrl = this.getAttribute('data-delete-url');
            
            // 更新模态框内容
            deleteMessage.textContent = `确定要删除平台 "${itemName}" 吗？此操作不可逆。`;
            deleteForm.action = deleteUrl;
            
            // 显示模态框
            deleteModal.show();
        });
    });
});
</script>
{% endblock %}
