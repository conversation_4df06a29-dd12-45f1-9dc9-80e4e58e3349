{% extends "base.html" %}

{% block title %}编辑平台 - 大语言模型管理系统{% endblock %}

{% block header %}编辑平台{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.edit_platform', id=platform.id) }}">
            {{ form.hidden_tag() }}
            <div class="mb-3">
                {{ form.name.label(class="form-label") }}
                {{ form.name(class="form-control") }}
                {% if form.name.errors %}
                    <div class="text-danger">
                        {% for error in form.name.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">平台的唯一标识名称，如 OpenAI、Azure 等</div>
            </div>

            <div class="mb-3">
                {{ form.base_url.label(class="form-label") }}
                {{ form.base_url(class="form-control") }}
                {% if form.base_url.errors %}
                    <div class="text-danger">
                        {% for error in form.base_url.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">OpenAI SDK中使用的Base URL，如 https://api.openai.com/v1</div>
            </div>

            <div class="mb-3">
                {{ form.api_key.label(class="form-label") }}
                {{ form.api_key(class="form-control") }}
                {% if form.api_key.errors %}
                    <div class="text-danger">
                        {% for error in form.api_key.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">用于访问平台API的密钥</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_platforms') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">更新</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
