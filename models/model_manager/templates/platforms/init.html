{% extends "base.html" %}

{% block title %}平台初始化 - 大语言模型管理系统{% endblock %}

{% block header %}平台初始化{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-server me-2"></i>
                    环境变量平台配置状态
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>平台名称</th>
                                <th>配置状态</th>
                                <th>平台标识</th>
                                <th>Base URL</th>
                                <th>API密钥</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for platform in env_status %}
                            <tr>
                                <td>
                                    <strong>{{ platform.display_name }}</strong>
                                </td>
                                <td>
                                    {% if platform.configured %}
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>已配置
                                        </span>
                                    {% else %}
                                        <span class="badge bg-warning">
                                            <i class="bi bi-exclamation-triangle me-1"></i>未配置
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if platform.configured %}
                                        <code>{{ platform.name }}</code>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if platform.configured %}
                                        <small class="text-muted">{{ platform.base_url[:50] }}{% if platform.base_url|length > 50 %}...{% endif %}</small>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if platform.configured and platform.has_api_key %}
                                        <span class="badge bg-success">
                                            <i class="bi bi-key me-1"></i>已设置
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle me-1"></i>未设置
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear me-2"></i>
                    初始化操作
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    从环境变量中读取平台配置并初始化到数据库中。如果平台已存在，将更新其配置信息。
                </p>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>注意：</strong>
                    <ul class="mb-0 mt-2">
                        <li>只有完整配置的平台才会被初始化</li>
                        <li>现有平台的配置将被更新</li>
                        <li>API密钥将被覆盖更新</li>
                    </ul>
                </div>
                
                <form method="post" action="{{ url_for('main.init_platforms') }}" onsubmit="return confirmInit()">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        执行平台初始化
                    </button>
                </form>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('main.list_platforms') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-list me-2"></i>
                        查看平台列表
                    </a>
                    <a href="{{ url_for('main.add_platform') }}" class="btn btn-outline-primary">
                        <i class="bi bi-plus me-2"></i>
                        手动添加平台
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-question-circle me-2"></i>
                    环境变量配置说明
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">
                    每个平台需要配置以下三个环境变量：
                </p>
                <ul class="small text-muted">
                    <li><code>*_PLATFORM_NAME</code> - 平台名称</li>
                    <li><code>*_BASE_URL</code> - API基础地址</li>
                    <li><code>*_API_KEY</code> - API访问密钥</li>
                </ul>
                <p class="small text-muted mb-0">
                    例如：<code>SPARK_PLATFORM_NAME</code>、<code>SPARK_BASE_URL</code>、<code>SPARK_API_KEY</code>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmInit() {
    return confirm('确定要执行平台初始化吗？\n\n这将会：\n- 从环境变量读取平台配置\n- 创建新平台或更新现有平台\n- 覆盖现有平台的API密钥');
}

// 自动刷新状态
document.addEventListener('DOMContentLoaded', function() {
    // 可以添加定时刷新功能
    setInterval(function() {
        // 这里可以通过AJAX更新状态
    }, 30000); // 30秒刷新一次
});
</script>
{% endblock %}