{% extends "base.html" %}

{% block title %}应用模型关联 - 大语言模型管理系统{% endblock %}

{% block header %}应用模型关联{% endblock %}

{% block content %}
<div class="d-flex justify-content-between mb-3">
    <h3>应用模型关联列表</h3>
    <a href="{{ url_for('main.add_app_model') }}" class="btn btn-primary">
        <i class="bi bi-plus"></i> 添加关联
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>应用名称</th>
                        <th>模型名称</th>
                        <th>是否默认</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for app_model in app_models %}
                    <tr>
                        <td>{{ app_model.id }}</td>
                        <td>{{ app_model.application.name }}</td>
                        <td>{{ app_model.model.display_name }}</td>
                        <td>
                            {% if app_model.is_default %}
                            <span class="badge bg-success">是</span>
                            {% else %}
                            <span class="badge bg-secondary">否</span>
                            {% endif %}
                        </td>
                        <td>{{ app_model.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        <td>
                            <a href="{{ url_for('main.edit_app_model', id=app_model.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-pencil"></i> 编辑
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                    data-item-id="{{ app_model.id }}"
                                    data-item-name="{{ app_model.application.name }} - {{ app_model.model.display_name }}"
                                    data-delete-url="{{ url_for('main.delete_app_model', id=app_model.id) }}">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center">暂无应用模型关联数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 通用删除确认对话框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteMessage">确定要删除此项目吗？此操作不可逆。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 处理删除按钮点击事件
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    const deleteForm = document.getElementById('deleteForm');
    const deleteMessage = document.getElementById('deleteMessage');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.getAttribute('data-item-id');
            const itemName = this.getAttribute('data-item-name');
            const deleteUrl = this.getAttribute('data-delete-url');
            
            // 更新模态框内容
            deleteMessage.textContent = `确定要删除应用模型关联 "${itemName}" 吗？此操作不可逆。`;
            deleteForm.action = deleteUrl;
            
            // 显示模态框
            deleteModal.show();
        });
    });
});
</script>
{% endblock %}
