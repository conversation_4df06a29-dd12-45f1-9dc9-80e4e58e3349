from flask import Blueprint, render_template, request, redirect, url_for, flash, session, current_app
from utils.security import security_manager
from utils.validators import LoginForm
import logging

logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    form = LoginForm()
    
    if request.method == 'POST':
        if form.validate_on_submit():
            password = form.password.data
            client_ip = request.environ.get('HTTP_X_REAL_IP', request.remote_addr)
            
            # 检查登录尝试次数
            if not security_manager.check_login_attempts(client_ip):
                flash('登录尝试次数过多，请稍后再试', 'danger')
                logger.warning(f"Too many login attempts from IP: {client_ip}")
                return render_template('login.html', form=form)
            
            # 验证密码
            admin_password = current_app.config.get('ADMIN_PASSWORD', 'admin123')
            if password == admin_password:
                # 登录成功
                session['logged_in'] = True
                session.permanent = True
                security_manager.record_login_attempt(client_ip, True)
                
                flash('登录成功', 'success')
                logger.info(f"Successful login from IP: {client_ip}")
                
                # 重定向到原来要访问的页面或首页
                next_page = request.args.get('next')
                if next_page:
                    return redirect(next_page)
                return redirect(url_for('main.index'))
            else:
                # 登录失败
                security_manager.record_login_attempt(client_ip, False)
                flash('密码错误', 'danger')
                logger.warning(f"Failed login attempt from IP: {client_ip}")
        else:
            # 表单验证失败
            for field, errors in form.errors.items():
                for error in errors:
                    flash(f'{field}: {error}', 'danger')
    
    return render_template('login.html', form=form)

@auth_bp.route('/logout')
def logout():
    """登出"""
    client_ip = request.environ.get('HTTP_X_REAL_IP', request.remote_addr)
    session.pop('logged_in', None)
    flash('已退出登录', 'success')
    logger.info(f"User logged out from IP: {client_ip}")
    return redirect(url_for('auth.login'))

@auth_bp.route('/check')
def check_auth():
    """检查认证状态（API用）"""
    if 'logged_in' in session:
        return {'authenticated': True}, 200
    else:
        return {'authenticated': False}, 401
