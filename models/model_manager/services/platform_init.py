import os
import logging
from models import db, Platform
from services.database import PlatformService

logger = logging.getLogger(__name__)

class PlatformInitService:
    """平台初始化服务"""
    
    @staticmethod
    def get_platform_configs_from_env():
        """从环境变量中获取平台配置"""
        platforms = []
        
        # 定义平台配置映射
        platform_configs = [
            {
                'name_env': 'DASHSCOPE_PLATFORM_NAME',
                'base_url_env': 'DASHSCOPE_BASE_URL',
                'api_key_env': 'DASHSCOPE_API_KEY'
            },
            {
                'name_env': 'DEEPSEEK_PLATFORM_NAME',
                'base_url_env': 'DEEPSEEK_BASE_URL',
                'api_key_env': 'DEEPSEEK_API_KEY'
            },
            {
                'name_env': 'GLM_PLATFORM_NAME',
                'base_url_env': 'GLM_BASE_URL',
                'api_key_env': 'GLM_API_KEY'
            },
            {
                'name_env': 'SPARK_PLATFORM_NAME',
                'base_url_env': 'SPARK_BASE_URL',
                'api_key_env': 'SPARK_API_KEY'
            },
            {
                'name_env': 'OPENROUTER_PLATFORM_NAME',
                'base_url_env': 'OPENROUTER_BASE_URL',
                'api_key_env': 'OPENROUTER_API_KEY'
            },
            {
                'name_env': 'DEEPBRICKS_PLATFORM_NAME',
                'base_url_env': 'DEEPBRICKS_BASE_URL',
                'api_key_env': 'DEEPBRICKS_API_KEY'
            }
        ]
        
        for config in platform_configs:
            name = os.environ.get(config['name_env'])
            base_url = os.environ.get(config['base_url_env'])
            api_key = os.environ.get(config['api_key_env'])
            
            if name and base_url and api_key:
                platforms.append({
                    'name': name,
                    'base_url': base_url,
                    'api_key': api_key
                })
                logger.info(f"从环境变量加载平台配置: {name}")
            else:
                logger.warning(f"平台配置不完整，跳过: {config['name_env']}")
        
        return platforms
    
    @staticmethod
    def init_platforms_from_env():
        """从环境变量初始化平台到数据库"""
        if not os.environ.get('INIT_PLATFORMS_ENABLED', 'false').lower() == 'true':
            logger.info("平台初始化已禁用")
            return True, "平台初始化已禁用"
        
        try:
            platforms_config = PlatformInitService.get_platform_configs_from_env()
            
            if not platforms_config:
                logger.warning("未找到有效的平台配置")
                return True, "未找到有效的平台配置"
            
            created_count = 0
            updated_count = 0
            
            for platform_config in platforms_config:
                # 检查平台是否已存在
                existing_platform = PlatformService.get_by_name(platform_config['name'])
                
                if existing_platform:
                    # 更新现有平台
                    success, error, updated_platform = PlatformService.update(
                        existing_platform.id,
                        platform_config['name'],
                        platform_config['base_url'],
                        platform_config['api_key']
                    )
                    
                    if success:
                        updated_count += 1
                        logger.info(f"更新平台: {platform_config['name']}")
                    else:
                        logger.error(f"更新平台失败 {platform_config['name']}: {error}")
                else:
                    # 创建新平台
                    success, error, new_platform = PlatformService.create(
                        platform_config['name'],
                        platform_config['base_url'],
                        platform_config['api_key']
                    )
                    
                    if success:
                        created_count += 1
                        logger.info(f"创建平台: {platform_config['name']}")
                    else:
                        logger.error(f"创建平台失败 {platform_config['name']}: {error}")
            
            message = f"平台初始化完成: 创建 {created_count} 个，更新 {updated_count} 个"
            logger.info(message)
            return True, message
            
        except Exception as e:
            error_msg = f"平台初始化失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def get_platform_env_status():
        """获取平台环境变量状态"""
        platform_configs = [
            {
                'name': 'DashScope',
                'name_env': 'DASHSCOPE_PLATFORM_NAME',
                'base_url_env': 'DASHSCOPE_BASE_URL',
                'api_key_env': 'DASHSCOPE_API_KEY'
            },
            {
                'name': 'DeepSeek',
                'name_env': 'DEEPSEEK_PLATFORM_NAME',
                'base_url_env': 'DEEPSEEK_BASE_URL',
                'api_key_env': 'DEEPSEEK_API_KEY'
            },
            {
                'name': 'GLM',
                'name_env': 'GLM_PLATFORM_NAME',
                'base_url_env': 'GLM_BASE_URL',
                'api_key_env': 'GLM_API_KEY'
            },
            {
                'name': 'Spark',
                'name_env': 'SPARK_PLATFORM_NAME',
                'base_url_env': 'SPARK_BASE_URL',
                'api_key_env': 'SPARK_API_KEY'
            },
            {
                'name': 'OpenRouter',
                'name_env': 'OPENROUTER_PLATFORM_NAME',
                'base_url_env': 'OPENROUTER_BASE_URL',
                'api_key_env': 'OPENROUTER_API_KEY'
            },
            {
                'name': 'DeepBricks',
                'name_env': 'DEEPBRICKS_PLATFORM_NAME',
                'base_url_env': 'DEEPBRICKS_BASE_URL',
                'api_key_env': 'DEEPBRICKS_API_KEY'
            }
        ]
        
        status = []
        for config in platform_configs:
            name = os.environ.get(config['name_env'])
            base_url = os.environ.get(config['base_url_env'])
            api_key = os.environ.get(config['api_key_env'])
            
            is_configured = bool(name and base_url and api_key)
            
            status.append({
                'display_name': config['name'],
                'env_name': config['name_env'],
                'configured': is_configured,
                'name': name if is_configured else None,
                'base_url': base_url if is_configured else None,
                'has_api_key': bool(api_key) if is_configured else False
            })
        
        return status