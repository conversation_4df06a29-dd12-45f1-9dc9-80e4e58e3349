import logging
import logging.handlers
import os
from datetime import datetime

def setup_logging(app):
    """设置应用日志配置"""
    
    # 创建logs目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置日志级别
    log_level = getattr(logging, app.config.get('LOG_LEVEL', 'INFO').upper())
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 详细格式化器（用于文件日志）
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s'
    )
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器 - 应用日志
    app_log_file = os.path.join(log_dir, 'app.log')
    app_file_handler = logging.handlers.RotatingFileHandler(
        app_log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    app_file_handler.setLevel(log_level)
    app_file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(app_file_handler)
    
    # 错误日志文件处理器
    error_log_file = os.path.join(log_dir, 'error.log')
    error_file_handler = logging.handlers.RotatingFileHandler(
        error_log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    error_file_handler.setLevel(logging.ERROR)
    error_file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_file_handler)
    
    # 安全日志文件处理器
    security_log_file = os.path.join(log_dir, 'security.log')
    security_file_handler = logging.handlers.RotatingFileHandler(
        security_log_file, maxBytes=10*1024*1024, backupCount=10, encoding='utf-8'
    )
    security_file_handler.setLevel(logging.INFO)
    security_file_handler.setFormatter(detailed_formatter)
    
    # 创建安全日志记录器
    security_logger = logging.getLogger('security')
    security_logger.addHandler(security_file_handler)
    security_logger.setLevel(logging.INFO)
    
    # API访问日志处理器
    api_log_file = os.path.join(log_dir, 'api.log')
    api_file_handler = logging.handlers.RotatingFileHandler(
        api_log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    api_file_handler.setLevel(logging.INFO)
    api_file_handler.setFormatter(detailed_formatter)
    
    # 创建API日志记录器
    api_logger = logging.getLogger('api')
    api_logger.addHandler(api_file_handler)
    api_logger.setLevel(logging.INFO)
    
    # 数据库日志处理器（仅在调试模式下）
    if app.debug:
        db_log_file = os.path.join(log_dir, 'database.log')
        db_file_handler = logging.handlers.RotatingFileHandler(
            db_log_file, maxBytes=10*1024*1024, backupCount=3, encoding='utf-8'
        )
        db_file_handler.setLevel(logging.DEBUG)
        db_file_handler.setFormatter(detailed_formatter)
        
        # SQLAlchemy日志
        sqlalchemy_logger = logging.getLogger('sqlalchemy.engine')
        sqlalchemy_logger.addHandler(db_file_handler)
        sqlalchemy_logger.setLevel(logging.INFO)
    
    # 设置第三方库日志级别
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    app.logger.info("日志系统初始化完成")

class SecurityLogger:
    """安全事件日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger('security')
    
    def log_login_attempt(self, ip_address, success, username=None):
        """记录登录尝试"""
        status = "成功" if success else "失败"
        user_info = f" 用户: {username}" if username else ""
        self.logger.info(f"登录尝试 - IP: {ip_address} - 状态: {status}{user_info}")
    
    def log_api_access(self, ip_address, endpoint, api_key=None, success=True):
        """记录API访问"""
        status = "成功" if success else "失败"
        key_info = f" API密钥: {api_key[:10]}..." if api_key else ""
        self.logger.info(f"API访问 - IP: {ip_address} - 端点: {endpoint} - 状态: {status}{key_info}")
    
    def log_security_event(self, event_type, ip_address, details):
        """记录安全事件"""
        self.logger.warning(f"安全事件 - 类型: {event_type} - IP: {ip_address} - 详情: {details}")
    
    def log_data_change(self, user_ip, action, resource_type, resource_id, details=None):
        """记录数据变更"""
        detail_info = f" - 详情: {details}" if details else ""
        self.logger.info(f"数据变更 - IP: {user_ip} - 操作: {action} - 资源: {resource_type}#{resource_id}{detail_info}")

class APILogger:
    """API访问日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger('api')
    
    def log_request(self, method, endpoint, ip_address, user_agent=None, response_code=None):
        """记录API请求"""
        ua_info = f" - UA: {user_agent}" if user_agent else ""
        code_info = f" - 响应码: {response_code}" if response_code else ""
        self.logger.info(f"API请求 - {method} {endpoint} - IP: {ip_address}{ua_info}{code_info}")
    
    def log_error(self, endpoint, error_message, ip_address):
        """记录API错误"""
        self.logger.error(f"API错误 - 端点: {endpoint} - IP: {ip_address} - 错误: {error_message}")

# 全局日志记录器实例
security_logger = SecurityLogger()
api_logger = APILogger()
