# app.py - 模型管理服务（只负责写入操作）
from flask import Flask
from flask_wtf.csrf import CSRFProtect
from models import db
from config import get_config
from utils.logging_config import setup_logging
from utils.error_handlers import register_error_handlers, handle_application_error
from blueprints.auth import auth_bp
from blueprints.main import main_bp
import os
from dotenv import load_dotenv

# 加载环境变量
def load_environment():
    """根据FLASK_ENV加载相应的环境变量文件"""
    flask_env = os.environ.get('FLASK_ENV', 'development')

    # 尝试加载特定环境的配置文件
    env_file = f'.env.{flask_env}'
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"Loaded environment from {env_file}")
    else:
        # 回退到默认的.env文件
        if os.path.exists('.env'):
            load_dotenv('.env')
            print("Loaded environment from .env")
        else:
            print("No .env file found, using system environment variables")

# 在应用创建前加载环境变量
load_environment()

def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    config_class = get_config()
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)

    # 初始化CSRF保护
    csrf = CSRFProtect(app)

    # 设置日志
    setup_logging(app)

    # 注册错误处理器
    register_error_handlers(app)
    handle_application_error(app)

    # 注册蓝图
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)

    # 创建数据库表
    with app.app_context():
        db.create_all()
        
        # 自动初始化平台（如果启用）
        try:
            from services.platform_init import PlatformInitService
            success, message = PlatformInitService.init_platforms_from_env()
            if success:
                app.logger.info(f"平台初始化: {message}")
            else:
                app.logger.warning(f"平台初始化失败: {message}")
        except Exception as e:
            app.logger.error(f"平台初始化异常: {str(e)}")

    return app

# 创建应用实例
app = create_app()

# 模型管理服务只负责写入操作，读取操作由API服务(5002端口)提供

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=20000)
