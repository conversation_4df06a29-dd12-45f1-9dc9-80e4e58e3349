services:
  # 聊天记录数据库
  records_db:
    image: mysql:8.0
    container_name: records_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: records_root_password
      MYSQL_DATABASE: vdb_records
      MYSQL_USER: records_user
      MYSQL_PASSWORD: records_password
    ports:
      - "3307:3306"
    volumes:
      - /vdb_records:/var/lib/mysql
      - ./records_database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./records_database/backup:/backup
    networks:
      - records_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "records_user", "-precords_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 记录API服务 (FastAPI)
  record-api-service:
    build:
      context: ./record_api_service
      dockerfile: Dockerfile
    container_name: record-api-service
    restart: unless-stopped
    ports:
      - "6002:6002"
    environment:
      - DATABASE_URL=mysql+pymysql://records_user:records_password@records_db:3306/vdb_records
      - API_SECRET_KEY=your-secret-key-change-in-production
      - DEBUG=true
      - PORT=6002
    volumes:
      - ./record_api_service/logs:/app/logs
      - ./record_api_service/uploads:/app/uploads
    networks:
      - records_network
    depends_on:
      records_db:
        condition: service_healthy

networks:
  records_network:
    name: records_network
    driver: bridge

volumes:
  records_data:
    driver: local
