#!/bin/bash

# 启动记录服务组 (2个镜像: records_db, record-api-service)

set -e

echo "=== 启动记录服务组 ==="
echo ""

# 检查Docker和Docker Compose是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装或未在PATH中"
    exit 1
fi

if ! docker compose version &> /dev/null; then
    echo "错误: Docker Compose 未安装或未在PATH中"
    exit 1
fi

# 检查记录数据目录是否存在，如果不存在则创建
echo "检查记录数据目录..."
if [ ! -d "/vdb_records" ]; then
    echo "创建聊天记录数据库目录: /vdb_records"
    sudo mkdir -p /vdb_records
    sudo chown -R 999:999 /vdb_records
fi
echo "记录数据目录检查完成"
echo ""

# 启动记录服务组
echo "启动记录服务组 (数据库 + API服务)..."
docker compose up -d --build

echo ""
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "=== 记录服务组状态 ==="
docker compose ps
echo ""

# 显示服务访问信息
echo "=== 服务访问信息 ==="
echo "  - 聊天记录数据库: localhost:3307 (用户: records_user)"
echo "  - 记录API服务: http://localhost:6002"
echo ""

# 健康检查
echo "=== 健康检查 ==="
echo "等待服务完全启动..."
sleep 30

echo "检查记录API服务健康状态..."
curl -f http://localhost:6002/health 2>/dev/null && echo "✓ 记录API服务正常" || echo "✗ 记录API服务异常"

echo ""
echo "=== 记录服务组启动完成 ==="
echo ""
echo "常用命令:"
echo "  查看服务状态: docker compose ps"
echo "  停止服务: ./stop.sh"
echo "  查看日志: docker compose logs -f"
echo "  查看特定服务日志: docker compose logs -f [service_name]"
