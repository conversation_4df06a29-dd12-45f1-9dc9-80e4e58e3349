-- 示例数据插入脚本
-- 用于测试v2.0.0优化后的数据库结构

USE vdb_records;

-- 插入测试用户
INSERT IGNORE INTO users (
    api_key,
    is_active,
    permission,
    mathjax,
    current_temperature,
    total_deposited,
    total_spent,
    current_balance,
    total_prompt_tokens,
    total_completion_tokens
) VALUES 
('test-user-001', TRUE, 1, FALSE, 0.70, 100.0000, 5.2500, 94.7500, 1250, 890),
('test-user-002', TRUE, 1, TRUE, 0.80, 50.0000, 2.1000, 47.9000, 680, 420);

-- 插入测试对话
INSERT IGNORE INTO conversations (
    user_id,
    title,
    created_at,
    latest_revised_at
) VALUES 
(1, '关于AI的讨论', '2024-01-15 10:00:00', '2024-01-15 10:30:00'),
(1, '编程问题咨询', '2024-01-15 14:00:00', '2024-01-15 14:45:00'),
(2, '数学公式推导', '2024-01-16 09:00:00', '2024-01-16 09:25:00');

-- 插入测试消息
INSERT INTO messages (
    conversation_id,
    role,
    created_at,
    original_url,
    rendered_html_url,
    plain_html_url,
    model_id,
    temperature,
    max_tokens,
    is_error,
    error_info
) VALUES 
-- 对话1的消息
(1, 'user', '2024-01-15 10:00:00', 'minio://bucket/msg_001_original.txt', 'minio://bucket/msg_001_rendered.html', 'minio://bucket/msg_001_plain.html', NULL, NULL, NULL, FALSE, NULL),
(1, 'assistant', '2024-01-15 10:01:00', 'minio://bucket/msg_002_original.txt', 'minio://bucket/msg_002_rendered.html', 'minio://bucket/msg_002_plain.html', 1, 0.70, 2000, FALSE, NULL),
(1, 'user', '2024-01-15 10:15:00', 'minio://bucket/msg_003_original.txt', 'minio://bucket/msg_003_rendered.html', 'minio://bucket/msg_003_plain.html', NULL, NULL, NULL, FALSE, NULL),
(1, 'assistant', '2024-01-15 10:16:00', 'minio://bucket/msg_004_original.txt', 'minio://bucket/msg_004_rendered.html', 'minio://bucket/msg_004_plain.html', 1, 0.70, 2000, FALSE, NULL),

-- 对话2的消息
(2, 'user', '2024-01-15 14:00:00', 'minio://bucket/msg_005_original.txt', 'minio://bucket/msg_005_rendered.html', 'minio://bucket/msg_005_plain.html', NULL, NULL, NULL, FALSE, NULL),
(2, 'assistant', '2024-01-15 14:01:00', 'minio://bucket/msg_006_original.txt', 'minio://bucket/msg_006_rendered.html', 'minio://bucket/msg_006_plain.html', 2, 0.70, 1500, FALSE, NULL),
(2, 'user', '2024-01-15 14:30:00', 'minio://bucket/msg_007_original.txt', 'minio://bucket/msg_007_rendered.html', 'minio://bucket/msg_007_plain.html', NULL, NULL, NULL, FALSE, NULL),
(2, 'assistant', '2024-01-15 14:31:00', 'minio://bucket/msg_008_original.txt', 'minio://bucket/msg_008_rendered.html', 'minio://bucket/msg_008_plain.html', 2, 0.70, 1500, TRUE, 'API rate limit exceeded'),

-- 对话3的消息（包含数学公式）
(3, 'user', '2024-01-16 09:00:00', 'minio://bucket/msg_009_original.txt', 'minio://bucket/msg_009_rendered.html', 'minio://bucket/msg_009_plain.html', NULL, NULL, NULL, FALSE, NULL),
(3, 'assistant', '2024-01-16 09:01:00', 'minio://bucket/msg_010_original.txt', 'minio://bucket/msg_010_rendered.html', 'minio://bucket/msg_010_plain.html', 3, 0.80, 2500, FALSE, NULL);

-- 插入费用记录（只为assistant消息）
INSERT INTO message_costs (
    message_id,
    prompt_tokens,
    completion_tokens,
    prompt_cost,
    completion_cost,
    total_cost,
    created_at
) VALUES 
-- 对话1的assistant消息费用
(2, 45, 156, 0.000090, 0.000468, 0.000558, '2024-01-15 10:01:00'),
(4, 67, 203, 0.000134, 0.000609, 0.000743, '2024-01-15 10:16:00'),

-- 对话2的assistant消息费用
(6, 89, 134, 0.000178, 0.000402, 0.000580, '2024-01-15 14:01:00'),
(8, 72, 0, 0.000144, 0.000000, 0.000144, '2024-01-15 14:31:00'), -- 错误消息，没有completion

-- 对话3的assistant消息费用
(10, 123, 287, 0.000246, 0.000861, 0.001107, '2024-01-16 09:01:00');

-- 更新用户的当前对话ID
UPDATE users SET current_conversation_id = 1 WHERE id = 1;
UPDATE users SET current_conversation_id = 3 WHERE id = 2;

-- 显示插入结果
SELECT '=== 示例数据插入完成 ===' as status;

SELECT '=== 用户统计 ===' as info;
SELECT 
    u.id,
    u.api_key,
    COUNT(DISTINCT c.id) as conversations_count,
    COUNT(CASE WHEN m.role = 'user' THEN 1 END) as user_messages,
    COUNT(CASE WHEN m.role = 'assistant' THEN 1 END) as assistant_messages,
    COALESCE(SUM(mc.total_cost), 0) as total_cost
FROM users u
LEFT JOIN conversations c ON u.id = c.user_id
LEFT JOIN messages m ON c.id = m.conversation_id
LEFT JOIN message_costs mc ON m.id = mc.message_id
GROUP BY u.id, u.api_key;

SELECT '=== 对话统计 ===' as info;
SELECT 
    c.id,
    c.title,
    COUNT(m.id) as total_messages,
    COUNT(CASE WHEN m.role = 'user' THEN 1 END) as user_messages,
    COUNT(CASE WHEN m.role = 'assistant' THEN 1 END) as assistant_messages,
    COALESCE(SUM(mc.total_cost), 0) as conversation_cost
FROM conversations c
LEFT JOIN messages m ON c.id = m.conversation_id
LEFT JOIN message_costs mc ON m.id = mc.message_id
GROUP BY c.id, c.title;

SELECT '=== 费用统计 ===' as info;
SELECT 
    COUNT(*) as cost_records,
    SUM(prompt_tokens) as total_prompt_tokens,
    SUM(completion_tokens) as total_completion_tokens,
    SUM(total_cost) as total_cost
FROM message_costs;
