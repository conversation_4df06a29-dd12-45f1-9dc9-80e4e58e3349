-- 验证新数据库结构脚本
-- 用于检查v2.0.0重构后的表结构是否正确

USE vdb_records;

-- 显示当前数据库版本
SELECT '=== 数据库版本信息 ===' as info;
SELECT * FROM system_info ORDER BY id DESC LIMIT 1;

-- 检查表是否存在
SELECT '=== 表结构检查 ===' as info;
SELECT
    table_name,
    CASE
        WHEN table_name IN ('messages', 'message_costs') THEN '✓ 核心表'
        WHEN table_name IN ('users', 'conversations', 'system_info') THEN '✓ 基础表'
        ELSE '✓ 其他表'
    END as status
FROM information_schema.tables
WHERE table_schema = 'vdb_records'
ORDER BY table_name;

-- 检查messages表结构
SELECT '=== messages 表结构 ===' as info;
DESCRIBE messages;

-- 检查message_costs表结构
SELECT '=== message_costs 表结构 ===' as info;
DESCRIBE message_costs;

-- 检查外键约束
SELECT '=== 外键约束检查 ===' as info;
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'vdb_records'
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 检查索引
SELECT '=== 索引检查 ===' as info;
SELECT
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'vdb_records'
AND TABLE_NAME IN ('messages', 'message_costs')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 数据统计
SELECT '=== 数据统计 ===' as info;
SELECT
    'messages' as table_name,
    COUNT(*) as record_count
FROM messages
UNION ALL
SELECT
    'message_costs' as table_name,
    COUNT(*) as record_count
FROM message_costs
UNION ALL
SELECT
    'conversations' as table_name,
    COUNT(*) as record_count
FROM conversations
UNION ALL
SELECT
    'users' as table_name,
    COUNT(*) as record_count
FROM users;

-- 按角色统计消息
SELECT '=== 消息角色统计 ===' as info;
SELECT
    role,
    COUNT(*) as count
FROM messages
GROUP BY role;

-- 检查数据完整性
SELECT '=== 数据完整性检查 ===' as info;

-- 检查是否有助手消息没有对应的费用记录
SELECT
    '助手消息缺少费用记录' as check_type,
    COUNT(*) as count
FROM messages m
LEFT JOIN message_costs mc ON m.id = mc.message_id
WHERE m.role = 'assistant' AND mc.id IS NULL;

-- 检查是否有费用记录对应的不是助手消息
SELECT
    '费用记录对应非助手消息' as check_type,
    COUNT(*) as count
FROM message_costs mc
JOIN messages m ON mc.message_id = m.id
WHERE m.role != 'assistant';

-- 检查original_url字段是否为空
SELECT
    '消息缺少original_url' as check_type,
    COUNT(*) as count
FROM messages
WHERE original_url IS NULL OR original_url = '';

-- 检查role字段是否有效
SELECT
    '无效的role值' as check_type,
    COUNT(*) as count
FROM messages
WHERE role NOT IN ('user', 'assistant');

SELECT '=== 验证完成 ===' as info;
SELECT
    CASE
        WHEN EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = 'vdb_records' AND table_name = 'messages')
        AND EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = 'vdb_records' AND table_name = 'message_costs')
        AND EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema = 'vdb_records' AND table_name = 'messages' AND column_name = 'role' AND column_type = "enum('user','assistant')")
        THEN '✓ 数据库结构优化成功'
        ELSE '✗ 数据库结构验证失败'
    END as final_status;
