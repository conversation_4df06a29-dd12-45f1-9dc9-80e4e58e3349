# 聊天记录数据库服务 (Records Database)

这是一个独立的MySQL数据库服务，专门用于存储用户聊天记录和相关信息。该服务为整个大语言模型管理系统提供数据持久化支持。

## 🔄 重要更新 - 数据库结构优化

**版本 v2.0.0** - 消息表结构已优化，主要变更：

1. **保持单一消息表**: 保留`messages`表统一存储，便于按时间排序查询
2. **移除content字段**: 消息内容通过`original_url`存储在MinIO中，不再在数据库中存储文本
3. **费用计算独立**: 新增`message_costs`表专门记录助手消息的费用信息
4. **角色类型限制**: 使用ENUM类型限制`role`字段只能是'user'或'assistant'
5. **URL字段重命名**: 更准确的字段命名：`rendered_html_url`（含KaTeX）和`plain_html_url`（不含KaTeX）
6. **约束保证**: 添加约束确保只有assistant消息才能有费用记录

> ✅ **架构优势**: 既保持了查询的简便性，又实现了费用计算的独立性。

## 🚀 核心功能

- **用户管理**: 存储用户信息、权限级别、API密钥和账户余额
- **对话管理**: 存储用户的对话会话和对话元数据
- **消息存储**: 存储聊天消息内容、token统计和费用信息
- **MinIO集成**: 支持存储消息的MinIO文件URL（原始、KaTeX渲染、纯文本渲染）
- **系统管理**: 存储系统版本信息和配置数据
- **数据统计**: 支持用户使用量统计和费用计算

## 📊 数据库配置信息

| 配置项 | 值 | 说明 |
|--------|-----|------|
| **数据库名** | `vdb_records` | 主数据库名称 |
| **服务端口** | `3307` | 外部访问端口（映射到容器内3306） |
| **用户名** | `records_user` | 数据库用户 |
| **密码** | `records_password` | 数据库密码 |
| **数据目录** | `/vdb_records` | 宿主机数据存储目录 |
| **字符集** | `utf8mb4` | 支持完整的UTF-8字符集 |
| **时区** | `UTC` | 统一使用UTC时区 |

## 🗄️ 数据表结构

### 1. users 表 - 用户信息
存储系统用户的基本信息、权限和账户数据。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | INT | 主键，自增 |
| `api_key` | VARCHAR(255) | 用户API密钥，唯一索引 |
| `created_at` | DATETIME | 创建时间 |
| `is_active` | BOOLEAN | 是否激活，默认TRUE |
| `permission` | INT | 权限级别（1-9），默认1 |
| `mathjax` | BOOLEAN | 是否启用MathJax，默认FALSE |
| `current_model_id` | INT | 当前使用的模型ID |
| `current_temperature` | DECIMAL(3,2) | 当前温度设置，默认0.70 |
| `current_conversation_id` | INT | 当前对话ID |
| `total_deposited` | DECIMAL(10,4) | 总充值金额 |
| `total_spent` | DECIMAL(10,4) | 总消费金额 |
| `current_balance` | DECIMAL(10,4) | 当前余额 |
| `total_prompt_tokens` | INT | 总输入token数 |
| `total_completion_tokens` | INT | 总输出token数 |

### 2. conversations 表 - 对话记录
存储用户的对话会话信息。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | INT | 主键，自增 |
| `created_at` | DATETIME | 创建时间 |
| `latest_revised_at` | DATETIME | 最后修改时间 |
| `user_id` | INT | 用户ID，外键关联users表 |
| `title` | VARCHAR(255) | 对话标题 |

### 3. messages 表 - 消息内容（优化后）
统一存储用户和助手消息，便于按时间排序查询。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | INT | 主键，自增 |
| `conversation_id` | INT | 对话ID，外键关联conversations表 |
| `role` | ENUM('user','assistant') | 消息角色，限制为用户或助手 |
| `created_at` | DATETIME | 创建时间 |
| `updated_at` | DATETIME | 更新时间 |
| `original_url` | VARCHAR(500) | 消息内容MinIO存储URL（必填） |
| `rendered_html_url` | VARCHAR(500) | 渲染后的HTML版本URL（包含KaTeX） |
| `plain_html_url` | VARCHAR(500) | 渲染后的HTML版本URL（不含KaTeX） |
| `model_id` | INT | 使用的模型ID（仅assistant消息有效） |
| `temperature` | DECIMAL(3,2) | 温度参数（仅assistant消息有效） |
| `max_tokens` | INT | 最大token数（仅assistant消息有效） |
| `is_error` | BOOLEAN | 是否为错误消息（仅assistant消息有效） |
| `error_info` | TEXT | 错误信息（仅assistant消息有效） |

### 4. message_costs 表 - 消息费用（优化后）
单独记录助手消息的费用信息，通过外键关联到messages表。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | INT | 主键，自增 |
| `message_id` | INT | 消息ID，外键关联messages表 |
| `prompt_tokens` | INT | 输入token数 |
| `completion_tokens` | INT | 输出token数 |
| `prompt_cost` | DECIMAL(10,6) | 输入费用 |
| `completion_cost` | DECIMAL(10,6) | 输出费用 |
| `total_cost` | DECIMAL(10,6) | 总费用 |
| `created_at` | DATETIME | 创建时间 |

> **约束说明**: message_costs表有约束确保只有role='assistant'的消息才能有费用记录。

### 5. system_info 表 - 系统信息
存储系统版本和配置信息。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | INT | 主键，自增 |
| `version` | VARCHAR(20) | 系统版本号 |
| `initialized_at` | DATETIME | 初始化时间 |
| `notes` | TEXT | 版本说明 |

## 🔧 数据库索引优化

为了提高查询性能，数据库包含以下关键索引：

### users 表索引
- `idx_user_api_key` - API密钥索引（唯一）
- `idx_user_active` - 活跃状态索引
- `idx_user_permission` - 权限级别索引
- `idx_user_created` - 创建时间索引

### conversations 表索引
- `idx_conversation_user` - 用户ID索引
- `idx_conversation_created` - 创建时间索引
- `idx_conversation_revised` - 最后修改时间索引

### messages 表索引
- `idx_message_conversation` - 对话ID索引
- `idx_message_role` - 消息角色索引
- `idx_message_created` - 创建时间索引
- `idx_message_conversation_created` - 对话ID+创建时间复合索引（优化排序查询）
- `idx_message_model` - 模型ID索引
- `idx_message_error` - 错误状态索引

### message_costs 表索引
- `idx_cost_message` - 消息ID索引
- `idx_cost_created` - 创建时间索引
- `idx_cost_total` - 总费用索引

## 🚀 快速启动

### 1. 准备环境

```bash
# 创建数据存储目录
sudo mkdir -p /vdb_records
sudo chown -R 999:999 /vdb_records

# 确保Docker和Docker Compose已安装
docker --version
docker-compose --version
```

### 2. 启动数据库服务

```bash
# 启动数据库服务（后台运行）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看启动日志
docker-compose logs -f records-db
```

### 3. 验证服务状态

```bash
# 检查容器状态
docker-compose ps

# 测试数据库连接
docker-compose exec records-db mysql -u records_user -p -e "SELECT VERSION();"
```

## 🔌 数据库连接

### 使用Docker连接

```bash
# 进入MySQL命令行
docker-compose exec records-db mysql -u records_user -p

# 连接后查看数据库
USE vdb_records;
SHOW TABLES;
DESCRIBE users;
```

### 使用外部客户端连接

```bash
# 命令行连接
mysql -h localhost -P 3307 -u records_user -p vdb_records

# 连接参数
Host: localhost
Port: 3307
Username: records_user
Password: records_password
Database: vdb_records
```

### 使用图形化工具连接

推荐使用以下工具：
- **MySQL Workbench**
- **phpMyAdmin**
- **DBeaver**
- **Navicat**

连接配置：
```
主机: localhost
端口: 3307
用户名: records_user
密码: records_password
数据库: vdb_records
```

## 🛠️ 数据库管理

### 停止服务

```bash
# 停止服务但保留数据
docker-compose stop

# 停止并删除容器（数据保留在/vdb_records）
docker-compose down

# 完全清理（包括数据卷，谨慎使用）
docker-compose down -v
```

### 重启服务

```bash
# 重启数据库服务
docker-compose restart records-db

# 重新构建并启动
docker-compose up -d --build
```

## 💾 数据备份与恢复

### 自动备份脚本

```bash
#!/bin/bash
# backup_records.sh

BACKUP_DIR="/backup/records"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="records_backup_${DATE}.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
docker-compose exec -T records-db mysqldump \
  -u records_user \
  -precords_password \
  --single-transaction \
  --routines \
  --triggers \
  vdb_records > "${BACKUP_DIR}/${BACKUP_FILE}"

# 压缩备份文件
gzip "${BACKUP_DIR}/${BACKUP_FILE}"

echo "备份完成: ${BACKUP_DIR}/${BACKUP_FILE}.gz"

# 清理7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
```

### 手动备份

```bash
# 创建备份目录
mkdir -p backup

# 完整备份
docker-compose exec records-db mysqldump \
  -u records_user \
  -p \
  --single-transaction \
  --routines \
  --triggers \
  vdb_records > backup/records_backup_$(date +%Y%m%d_%H%M%S).sql

# 仅备份数据（不包含结构）
docker-compose exec records-db mysqldump \
  -u records_user \
  -p \
  --no-create-info \
  vdb_records > backup/records_data_$(date +%Y%m%d_%H%M%S).sql

# 仅备份结构（不包含数据）
docker-compose exec records-db mysqldump \
  -u records_user \
  -p \
  --no-data \
  vdb_records > backup/records_schema_$(date +%Y%m%d_%H%M%S).sql
```

### 数据恢复

```bash
# 从备份文件恢复
docker-compose exec -i records-db mysql \
  -u records_user \
  -p \
  vdb_records < backup/records_backup_20240101_120000.sql

# 恢复压缩备份
gunzip -c backup/records_backup_20240101_120000.sql.gz | \
docker-compose exec -i records-db mysql \
  -u records_user \
  -p \
  vdb_records
```

## 📊 数据库监控

### 性能监控查询

```sql
-- 查看数据库大小
SELECT
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'vdb_records'
GROUP BY table_schema;

-- 查看各表大小
SELECT
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    table_rows AS 'Rows'
FROM information_schema.TABLES
WHERE table_schema = 'vdb_records'
ORDER BY (data_length + index_length) DESC;

-- 查看连接状态
SHOW PROCESSLIST;

-- 查看数据库状态
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Queries';
```

### 日志监控

```bash
# 查看MySQL错误日志
docker-compose logs records-db | grep ERROR

# 实时监控日志
docker-compose logs -f records-db

# 查看慢查询（如果启用）
docker-compose exec records-db mysql -u records_user -p -e "SHOW VARIABLES LIKE 'slow_query_log';"
```

## 🔧 数据库维护

### 定期维护任务

```sql
-- 优化表
OPTIMIZE TABLE users;
OPTIMIZE TABLE conversations;
OPTIMIZE TABLE messages;
OPTIMIZE TABLE system_info;

-- 分析表
ANALYZE TABLE users;
ANALYZE TABLE conversations;
ANALYZE TABLE messages;

-- 检查表
CHECK TABLE users;
CHECK TABLE conversations;
CHECK TABLE messages;

-- 修复表（如果需要）
REPAIR TABLE table_name;
```

### 清理历史数据

```sql
-- 清理30天前的错误消息
DELETE FROM messages
WHERE is_error = TRUE
AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理无消息的空对话
DELETE c FROM conversations c
LEFT JOIN messages m ON c.id = m.conversation_id
WHERE m.id IS NULL
AND c.created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 清理非活跃用户的旧数据（谨慎使用）
DELETE FROM conversations
WHERE user_id IN (
    SELECT id FROM users
    WHERE is_active = FALSE
    AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
);
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 容器启动失败

```bash
# 检查端口占用
netstat -tulpn | grep 3307

# 检查数据目录权限
ls -la /vdb_records

# 重新设置权限
sudo chown -R 999:999 /vdb_records
sudo chmod -R 755 /vdb_records
```

#### 2. 连接被拒绝

```bash
# 检查服务状态
docker-compose ps

# 检查网络连接
docker-compose exec records-db netstat -tulpn

# 重启服务
docker-compose restart records-db
```

#### 3. 数据库损坏

```bash
# 检查表状态
docker-compose exec records-db mysql -u records_user -p -e "CHECK TABLE vdb_records.messages;"

# 修复表
docker-compose exec records-db mysql -u records_user -p -e "REPAIR TABLE vdb_records.messages;"

# 从备份恢复
# （参考数据恢复部分）
```

#### 4. 磁盘空间不足

```bash
# 检查磁盘使用情况
df -h /vdb_records

# 清理Docker日志
docker system prune -f

# 清理旧的备份文件
find /backup -name "*.gz" -mtime +30 -delete
```

## 🔐 安全配置

### 生产环境安全建议

1. **修改默认密码**
```sql
-- 修改数据库用户密码
ALTER USER 'records_user'@'%' IDENTIFIED BY 'your_secure_password';
FLUSH PRIVILEGES;
```

2. **限制网络访问**
```yaml
# docker-compose.yml 中限制端口绑定
ports:
  - "127.0.0.1:3307:3306"  # 仅本地访问
```

3. **启用SSL连接**
```sql
-- 检查SSL状态
SHOW VARIABLES LIKE 'have_ssl';
```

4. **定期更新**
```bash
# 更新Docker镜像
docker-compose pull
docker-compose up -d
```

## 📝 常用查询示例（v2.0.0优化结构）

### 基础查询

```sql
-- 查询对话的所有消息（按时间排序）- 现在非常简单！
SELECT
    id,
    role,
    created_at,
    original_url,
    rendered_html_url,
    plain_html_url
FROM messages
WHERE conversation_id = 1
ORDER BY created_at;

-- 查询助手消息及其费用信息
SELECT
    m.id,
    m.conversation_id,
    m.model_id,
    m.created_at,
    m.original_url,
    mc.prompt_tokens,
    mc.completion_tokens,
    mc.total_cost
FROM messages m
LEFT JOIN message_costs mc ON m.id = mc.message_id
WHERE m.conversation_id = 1 AND m.role = 'assistant';

-- 查询用户的总费用统计
SELECT
    u.id as user_id,
    u.api_key,
    COUNT(m.id) as total_assistant_messages,
    SUM(mc.prompt_tokens) as total_prompt_tokens,
    SUM(mc.completion_tokens) as total_completion_tokens,
    SUM(mc.total_cost) as total_cost
FROM users u
JOIN conversations c ON u.id = c.user_id
JOIN messages m ON c.id = m.conversation_id AND m.role = 'assistant'
LEFT JOIN message_costs mc ON m.id = mc.message_id
WHERE u.id = 1
GROUP BY u.id;
```

### 高级查询

```sql
-- 查询最近7天的费用统计
SELECT
    DATE(mc.created_at) as date,
    COUNT(mc.id) as message_count,
    SUM(mc.prompt_tokens) as total_prompt_tokens,
    SUM(mc.completion_tokens) as total_completion_tokens,
    SUM(mc.total_cost) as daily_cost
FROM message_costs mc
WHERE mc.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(mc.created_at)
ORDER BY date DESC;

-- 查询各模型的使用统计
SELECT
    m.model_id,
    COUNT(m.id) as message_count,
    AVG(mc.total_cost) as avg_cost_per_message,
    SUM(mc.total_cost) as total_cost
FROM messages m
LEFT JOIN message_costs mc ON m.id = mc.message_id
WHERE m.role = 'assistant'
GROUP BY m.model_id
ORDER BY total_cost DESC;

-- 查询错误消息统计
SELECT
    DATE(created_at) as date,
    model_id,
    COUNT(*) as error_count
FROM messages
WHERE role = 'assistant'
AND is_error = TRUE
AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), model_id
ORDER BY date DESC, error_count DESC;

-- 查询对话消息数量统计
SELECT
    conversation_id,
    COUNT(CASE WHEN role = 'user' THEN 1 END) as user_messages,
    COUNT(CASE WHEN role = 'assistant' THEN 1 END) as assistant_messages,
    COUNT(*) as total_messages
FROM messages
GROUP BY conversation_id
ORDER BY total_messages DESC;
```

### 数据清理查询

```sql
-- 清理30天前的错误消息及其费用记录
DELETE mc FROM message_costs mc
JOIN messages m ON mc.message_id = m.id
WHERE m.is_error = TRUE
AND m.created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

DELETE FROM messages
WHERE role = 'assistant'
AND is_error = TRUE
AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理无消息的空对话
DELETE c FROM conversations c
LEFT JOIN messages m ON c.id = m.conversation_id
WHERE m.id IS NULL
AND c.created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
```

## 📈 性能优化

### MySQL配置优化

```sql
-- 查看当前配置
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'max_connections';

-- 建议的生产环境配置
SET GLOBAL innodb_buffer_pool_size = 128M;  -- 根据内存调整
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 32M;
```

### 索引优化建议

```sql
-- 分析查询性能
EXPLAIN SELECT * FROM messages WHERE conversation_id = 1;

-- 添加复合索引（如果需要）
CREATE INDEX idx_message_conversation_role ON messages(conversation_id, role);
CREATE INDEX idx_message_created_role ON messages(created_at, role);
```

## 🔗 与其他服务的集成

### 服务依赖关系

```mermaid
graph TD
    A[record-api-service] --> B[records-database]
    C[model-api-service] --> A
    D[model-manager] --> A
    E[MinIO Storage] --> A
```

### 网络配置

```yaml
# docker-compose.yml 网络配置示例
networks:
  vdb_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## 📝 版本更新

### 数据库架构版本管理

当前版本：**v2.0.0** (消息表结构优化)

#### 版本历史
- **v1.0.0**: 基础数据库结构
- **v1.1.0**: 新增MinIO URL字段支持
- **v2.0.0**: 消息表结构优化，保持单表便于排序，费用计算独立化，URL字段重命名

#### 升级步骤

```sql
-- 检查当前版本
SELECT * FROM system_info ORDER BY id DESC LIMIT 1;

-- 升级到v2.0.0（消息表结构优化）
-- 1. 备份现有数据
CREATE TABLE messages_backup AS SELECT * FROM messages;

-- 2. 修改表结构
ALTER TABLE messages
MODIFY COLUMN role ENUM('user', 'assistant') NOT NULL,
ADD COLUMN rendered_html_url VARCHAR(500) COMMENT '渲染后的HTML版本URL（包含KaTeX）',
ADD COLUMN plain_html_url VARCHAR(500) COMMENT '渲染后的HTML版本URL（不含KaTeX）',
DROP COLUMN content,
DROP COLUMN prompt_tokens,
DROP COLUMN completion_tokens,
DROP COLUMN prompt_cost,
DROP COLUMN completion_cost,
DROP COLUMN total_cost;

-- 3. 创建费用表
CREATE TABLE message_costs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    prompt_tokens INT DEFAULT 0,
    completion_tokens INT DEFAULT 0,
    prompt_cost DECIMAL(10,6) DEFAULT 0.000000,
    completion_cost DECIMAL(10,6) DEFAULT 0.000000,
    total_cost DECIMAL(10,6) DEFAULT 0.000000,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
);

-- 4. 更新版本信息
INSERT INTO system_info (version, notes) VALUES
('2.0.0', 'Message table optimization: unified table with separate cost tracking');
```

> **注意**: 由于这是测试环境，可以直接重新初始化数据库而无需迁移数据。

## 📞 技术支持

### 日志收集

```bash
# 收集完整的诊断信息
echo "=== Docker版本 ===" > diagnosis.log
docker --version >> diagnosis.log
echo "=== 容器状态 ===" >> diagnosis.log
docker-compose ps >> diagnosis.log
echo "=== 数据库日志 ===" >> diagnosis.log
docker-compose logs records-db >> diagnosis.log
echo "=== 系统资源 ===" >> diagnosis.log
df -h >> diagnosis.log
free -h >> diagnosis.log
```

### 联系信息

如遇到问题，请提供以下信息：
- 错误日志
- 系统环境信息
- 数据库版本
- 操作步骤

---

## 📋 快速参考

### 常用命令

```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f records-db

# 连接数据库
docker-compose exec records-db mysql -u records_user -p

# 备份数据
docker-compose exec records-db mysqldump -u records_user -p vdb_records > backup.sql

# 停止服务
docker-compose down
```

### 重要端口

| 服务 | 端口 | 说明 |
|------|------|------|
| MySQL | 3307 | 数据库访问端口 |
| record-api-service | 5003 | API服务端口 |

### 默认账户

| 类型 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 数据库用户 | records_user | records_password | 数据库操作 |
| 管理员API | admin-api-key-change-in-production | - | 系统管理 |
