# Record API Service

基于FastAPI的聊天记录数据库API服务，为聊天服务器提供用户、对话和消息的完整CRUD功能。

> **注意**: 本服务目录已重构为规范的Python命名格式 (`record_api_service`)，符合PEP 8标准。

## 🚀 功能特性

- **用户管理**: 用户认证、信息管理、权限控制
- **对话管理**: 对话创建、查询、更新、删除
- **消息管理**: 消息发送、查询、按时间排序
- **费用统计**: 独立的费用记录和统计
- **权限控制**: 基于API Key的多级权限管理
- **高性能**: FastAPI异步处理，数据库连接池
- **完整文档**: 自动生成的OpenAPI文档

## 📊 技术栈

- **框架**: FastAPI + Uvicorn
- **数据库**: MySQL 8.0 + SQLAlchemy 2.0
- **认证**: HTTP Bearer Token (API Key)
- **部署**: Docker + Docker Compose
- **文档**: 自动生成的Swagger UI

## 🗄️ 数据库架构

### 核心表结构

1. **users** - 用户信息和权限
2. **conversations** - 对话记录
3. **messages** - 统一消息表（用户+助手）
4. **message_costs** - 费用记录（仅助手消息）

### 设计特点

- ✅ 消息按时间统一排序（解决分表查询复杂性）
- ✅ 内容存储在MinIO，数据库仅存URL
- ✅ 支持KaTeX和纯HTML两种渲染格式
- ✅ 只有assistant消息产生费用
- ✅ 完整的外键约束和数据完整性

## 🔧 快速启动

### 1. 环境准备

```bash
cd /imagefile/services/record_api_service

# 复制环境配置
cp .env.example .env
```

### 2. Docker部署（推荐）

```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

## 📖 API文档

启动服务后访问：

- **Swagger UI**: http://localhost:6002/docs
- **ReDoc**: http://localhost:6002/redoc
- **健康检查**: http://localhost:6002/health

## 🔐 认证方式

所有API请求需要在请求头中包含API Key：

```bash
Authorization: Bearer your-api-key
```

### 默认账户

- **管理员**: `admin-api-key-change-in-production` (权限级别: 9)
- **测试用户**: `test-user-001`, `test-user-002` (权限级别: 1)

> ⚠️ **生产环境请务必修改默认API Key！**

## 📝 详细API使用说明

### 1. 系统接口

#### 健康检查
```bash
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "service": "Record FastAPI Service",
  "version": "1.0.0",
  "database": {
    "connected": true,
    "info": {
      "version": "8.0.40",
      "database": "vdb_records"
    }
  }
}
```

#### 服务信息
```bash
GET /info
```

### 2. 用户管理接口

#### 获取当前用户信息
```bash
GET /api/v1/users/me
Authorization: Bearer admin-api-key-change-in-production
```

**响应示例**:
```json
{
  "id": 1,
  "api_key": "admin-api-key-change-in-production",
  "is_active": true,
  "permission": 9,
  "mathjax": false,
  "current_model_id": null,
  "current_temperature": "0.70",
  "current_conversation_id": null,
  "total_deposited": "0.0000",
  "total_spent": "0.0000",
  "current_balance": "0.0000",
  "total_prompt_tokens": 0,
  "total_completion_tokens": 0,
  "created_at": "2025-07-19T14:55:51"
}
```

#### 更新当前用户信息
```bash
PUT /api/v1/users/me
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "mathjax": true,
  "current_temperature": 0.8
}
```

#### 获取用户统计信息
```bash
GET /api/v1/users/me/stats
Authorization: Bearer admin-api-key-change-in-production
```

**响应示例**:
```json
{
  "user_id": 1,
  "total_conversations": 5,
  "total_messages": 20,
  "total_user_messages": 10,
  "total_assistant_messages": 10,
  "total_cost": "0.005000",
  "total_prompt_tokens": 500,
  "total_completion_tokens": 750
}
```

#### 创建用户（仅管理员）
```bash
POST /api/v1/users/
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "api_key": "new-user-api-key",
  "is_active": true,
  "permission": 1,
  "mathjax": false,
  "current_temperature": 0.7
}
```

### 3. 对话管理接口

#### 获取对话列表
```bash
GET /api/v1/conversations/?page=1&size=20
Authorization: Bearer admin-api-key-change-in-production
```

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页大小（默认20，最大100）
- `user_id`: 用户ID（管理员可指定，普通用户只能查看自己的）

#### 创建对话
```bash
POST /api/v1/conversations/
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "user_id": 1,
  "title": "关于AI的讨论"
}
```

**响应示例**:
```json
{
  "id": 21,
  "created_at": "2025-07-19T14:55:51",
  "latest_revised_at": "2025-07-19T14:55:51",
  "user_id": 1,
  "title": "关于AI的讨论",
  "message_count": 0
}
```

#### 更新对话
```bash
PUT /api/v1/conversations/21
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "title": "更新后的对话标题"
}
```

#### 删除对话
```bash
DELETE /api/v1/conversations/21
Authorization: Bearer admin-api-key-change-in-production
```

#### 获取对话统计信息
```bash
GET /api/v1/conversations/21/stats
Authorization: Bearer admin-api-key-change-in-production
```

### 4. 消息管理接口

#### 获取消息列表
```bash
GET /api/v1/messages/?conversation_id=21&page=1&size=50
Authorization: Bearer admin-api-key-change-in-production
```

**查询参数**:
- `conversation_id`: 对话ID（可选）
- `role`: 消息角色 - `user` 或 `assistant`（可选）
- `page`: 页码（默认1）
- `size`: 每页大小（默认50，最大200）

**响应示例**:
```json
[
  {
    "id": 29,
    "conversation_id": 21,
    "role": "user",
    "created_at": "2025-07-19T14:56:15",
    "updated_at": "2025-07-19T14:56:15",
    "original_url": "minio://bucket/test_message.txt",
    "rendered_katex_url": "minio://bucket/test_message.html",
    "rendered_plain_url": "minio://bucket/test_message_plain.html",
    "model_id": null,
    "temperature": null,
    "max_tokens": null,
    "is_error": false,
    "error_info": null,
    "cost": null
  },
  {
    "id": 30,
    "conversation_id": 21,
    "role": "assistant",
    "created_at": "2025-07-19T14:56:30",
    "updated_at": "2025-07-19T14:56:30",
    "original_url": "minio://bucket/assistant_message.txt",
    "rendered_katex_url": "minio://bucket/assistant_message.html",
    "rendered_plain_url": "minio://bucket/assistant_message_plain.html",
    "model_id": 1,
    "temperature": "0.70",
    "max_tokens": 2000,
    "is_error": false,
    "error_info": null,
    "cost": {
      "id": 1,
      "message_id": 30,
      "prompt_tokens": 100,
      "completion_tokens": 150,
      "prompt_cost": "0.000200",
      "completion_cost": "0.000450",
      "total_cost": "0.000650",
      "created_at": "2025-07-19T14:57:00"
    }
  }
]
```

#### 创建用户消息
```bash
POST /api/v1/messages/
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "conversation_id": 21,
  "role": "user",
  "original_url": "minio://bucket/user_message.txt",
  "rendered_katex_url": "minio://bucket/user_message.html",
  "rendered_plain_url": "minio://bucket/user_message_plain.html"
}
```

#### 创建助手消息
```bash
POST /api/v1/messages/
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "conversation_id": 21,
  "role": "assistant",
  "original_url": "minio://bucket/assistant_message.txt",
  "rendered_katex_url": "minio://bucket/assistant_message.html",
  "rendered_plain_url": "minio://bucket/assistant_message_plain.html",
  "model_id": 1,
  "temperature": 0.7,
  "max_tokens": 2000,
  "is_error": false
}
```

#### 更新消息
```bash
PUT /api/v1/messages/30
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "rendered_katex_url": "minio://bucket/updated_message.html",
  "is_error": false
}
```

#### 删除消息
```bash
DELETE /api/v1/messages/30
Authorization: Bearer admin-api-key-change-in-production
```

### 5. 费用管理接口

#### 创建费用记录
```bash
POST /api/v1/messages/30/cost
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "message_id": 30,
  "prompt_tokens": 100,
  "completion_tokens": 150,
  "prompt_cost": 0.0002,
  "completion_cost": 0.00045,
  "total_cost": 0.00065
}
```

#### 更新费用记录
```bash
PUT /api/v1/messages/30/cost
Authorization: Bearer admin-api-key-change-in-production
Content-Type: application/json

{
  "prompt_tokens": 120,
  "completion_tokens": 180,
  "total_cost": 0.00078
}
```

## 🔧 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `DATABASE_URL` | - | 数据库连接URL |
| `API_SECRET_KEY` | - | API密钥签名密钥 |
| `DEBUG` | false | 调试模式 |
| `HOST` | 0.0.0.0 | 服务监听地址 |
| `PORT` | 7022 | 服务端口 |
| `LOG_LEVEL` | INFO | 日志级别 |

### 权限级别说明

- **1-8**: 普通用户，只能访问自己的数据
- **9**: 管理员，可以访问所有数据和管理功能

## 🚨 错误处理

### 常见错误码

- **401 Unauthorized**: API Key无效或缺失
- **403 Forbidden**: 权限不足
- **404 Not Found**: 资源不存在
- **422 Validation Error**: 请求参数验证失败
- **500 Internal Server Error**: 服务器内部错误

### 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

## 📊 监控和维护

### 健康检查

```bash
# 服务健康状态
curl http://localhost:7022/health

# 数据库连接状态
curl http://localhost:7022/info
```

### 日志查看

```bash
# 查看容器日志
docker-compose logs -f record-fastapi-service

# 查看应用日志
tail -f logs/record-fastapi-service.log
```

### 性能监控

```bash
# 容器资源使用
docker stats record-fastapi-service

# 数据库连接数
docker exec records-db mysql -u records_user -precords_password -e "SHOW PROCESSLIST;"
```

## 🔄 与其他服务集成

### 网络配置

服务使用`vdb-services-network`网络，与其他服务通信：

```yaml
networks:
  shared-network:
    name: vdb-services-network
    external: true
```

### 服务依赖

- **records-database**: MySQL数据库服务
- **MinIO**: 文件存储服务（用于消息内容）

## 📞 技术支持

- **项目地址**: `/imagefile/services/record-fastapi-service`
- **API文档**: http://localhost:7022/docs
- **健康检查**: http://localhost:7022/health
- **服务端口**: 7022
