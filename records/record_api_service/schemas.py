"""
Pydantic模式定义
"""
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from models import MessageRole


# 基础模式
class BaseSchema(BaseModel):
    """基础模式"""
    model_config = ConfigDict(from_attributes=True)


# 用户相关模式
class UserBase(BaseSchema):
    """用户基础模式"""
    api_key: str = Field(..., description="用户API密钥")
    is_active: bool = Field(True, description="是否激活")
    permission: int = Field(1, description="权限级别")
    mathjax: bool = Field(False, description="是否启用MathJax")
    current_model_id: Optional[int] = Field(None, description="当前使用的模型ID")
    current_temperature: Decimal = Field(Decimal("0.70"), description="当前温度设置")


class UserCreate(UserBase):
    """创建用户模式"""
    pass


class UserUpdate(BaseSchema):
    """更新用户模式"""
    is_active: Optional[bool] = None
    permission: Optional[int] = None
    mathjax: Optional[bool] = None
    current_model_id: Optional[int] = None
    current_temperature: Optional[Decimal] = None
    current_conversation_id: Optional[int] = None


class UserResponse(UserBase):
    """用户响应模式"""
    id: int
    created_at: datetime
    current_conversation_id: Optional[int] = None
    total_deposited: Decimal
    total_spent: Decimal
    current_balance: Decimal
    total_prompt_tokens: int
    total_completion_tokens: int


# 对话相关模式
class ConversationBase(BaseSchema):
    """对话基础模式"""
    title: Optional[str] = Field(None, description="对话标题")


class ConversationCreate(ConversationBase):
    """创建对话模式"""
    user_id: int = Field(..., description="用户ID")


class ConversationUpdate(BaseSchema):
    """更新对话模式"""
    title: Optional[str] = None


class ConversationResponse(ConversationBase):
    """对话响应模式"""
    id: int
    created_at: datetime
    latest_revised_at: datetime
    user_id: int
    message_count: Optional[int] = Field(None, description="消息数量")


# 消息相关模式
class MessageBase(BaseSchema):
    """消息基础模式"""
    role: MessageRole = Field(..., description="消息角色")
    original_url: str = Field(..., description="消息内容MinIO存储URL")
    rendered_katex_url: Optional[str] = Field(None, description="渲染后的HTML版本URL（包含KaTeX）")
    rendered_plain_url: Optional[str] = Field(None, description="渲染后的HTML版本URL（不含KaTeX）")


class MessageCreate(MessageBase):
    """创建消息模式"""
    conversation_id: int = Field(..., description="对话ID")
    model_id: Optional[int] = Field(None, description="使用的模型ID（仅assistant消息）")
    temperature: Optional[Decimal] = Field(None, description="温度参数（仅assistant消息）")
    max_tokens: Optional[int] = Field(None, description="最大token数（仅assistant消息）")
    is_error: bool = Field(False, description="是否出错（仅assistant消息）")
    error_info: Optional[str] = Field(None, description="错误信息（仅assistant消息）")


class MessageUpdate(BaseSchema):
    """更新消息模式"""
    rendered_katex_url: Optional[str] = None
    rendered_plain_url: Optional[str] = None
    is_error: Optional[bool] = None
    error_info: Optional[str] = None


class MessageResponse(BaseSchema):
    """消息响应模式"""
    id: int
    conversation_id: int
    role: MessageRole
    created_at: datetime
    updated_at: datetime
    original_url: str
    rendered_katex_url: Optional[str] = None
    rendered_plain_url: Optional[str] = None
    model_id: Optional[int] = None
    temperature: Optional[Decimal] = None
    max_tokens: Optional[int] = None
    is_error: bool = False
    error_info: Optional[str] = None


# 消息费用相关模式
class MessageCostBase(BaseSchema):
    """消息费用基础模式"""
    prompt_tokens: int = Field(0, description="输入token数")
    completion_tokens: int = Field(0, description="输出token数")
    prompt_cost: Decimal = Field(Decimal("0.000000"), description="输入费用")
    completion_cost: Decimal = Field(Decimal("0.000000"), description="输出费用")
    total_cost: Decimal = Field(Decimal("0.000000"), description="总费用")


class MessageCostCreate(MessageCostBase):
    """创建消息费用模式"""
    message_id: int = Field(..., description="消息ID")


class MessageCostUpdate(BaseSchema):
    """更新消息费用模式"""
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    prompt_cost: Optional[Decimal] = None
    completion_cost: Optional[Decimal] = None
    total_cost: Optional[Decimal] = None


class MessageCostResponse(MessageCostBase):
    """消息费用响应模式"""
    id: int
    message_id: int
    created_at: datetime


# 带费用的消息响应模式
class MessageWithCostResponse(MessageResponse):
    """带费用的消息响应模式"""
    cost: Optional[MessageCostResponse] = None


# 统计相关模式
class UserStatsResponse(BaseSchema):
    """用户统计响应模式"""
    user_id: int
    total_conversations: int
    total_messages: int
    total_user_messages: int
    total_assistant_messages: int
    total_cost: Decimal
    total_prompt_tokens: int
    total_completion_tokens: int


class ConversationStatsResponse(BaseSchema):
    """对话统计响应模式"""
    conversation_id: int
    total_messages: int
    user_messages: int
    assistant_messages: int
    total_cost: Decimal


# 分页相关模式
class PaginationParams(BaseSchema):
    """分页参数"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")


class PaginatedResponse(BaseSchema):
    """分页响应模式"""
    items: List[BaseSchema]
    total: int
    page: int
    size: int
    pages: int


# 响应模式
class SuccessResponse(BaseSchema):
    """成功响应模式"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[BaseSchema] = None


class ErrorResponse(BaseSchema):
    """错误响应模式"""
    success: bool = False
    message: str
    error_code: Optional[str] = None
