"""
对话相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from database import get_db
from auth import get_current_user, require_admin, check_conversation_access
from models import User, Conversation
from schemas import (
    ConversationCreate, ConversationUpdate, ConversationResponse, 
    ConversationStatsResponse, SuccessResponse
)
from crud import ConversationCRUD

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/conversations", tags=["对话管理"])


@router.get(
    "/",
    response_model=List[ConversationResponse],
    summary="获取对话列表",
    description="""
    获取用户的对话列表，支持分页查询。

    **功能说明：**
    - 返回用户的对话列表
    - 支持分页查询，避免数据量过大
    - 包含每个对话的基本信息和统计数据
    - 管理员可以查询任意用户的对话

    **权限控制：**
    - 普通用户只能查看自己的对话
    - 管理员可以通过user_id参数查看任意用户的对话
    - 如果不指定user_id，管理员查看所有对话，普通用户查看自己的对话

    **分页参数：**
    - `page`: 页码，从1开始
    - `size`: 每页大小，范围1-100

    **筛选参数：**
    - `user_id`: 用户ID（仅管理员可指定）

    **返回信息：**
    - 对话基本信息（ID、标题、创建时间等）
    - 对话统计数据（消息数量、最后活动时间等）
    - 对话相关设置（模型、温度等）

    **使用场景：**
    - 用户历史对话列表
    - 对话管理和搜索
    - 管理员用户活动监控
    """,
    response_description="分页的对话信息列表"
)
async def get_conversations(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    user_id: Optional[int] = Query(None, description="用户ID（管理员可指定）"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取对话列表

    返回用户的对话列表，包括：
    - 对话基本信息
    - 消息数量统计
    - 最后活动时间
    - 对话相关设置

    权限控制：
    - 普通用户只能查看自己的对话
    - 管理员可以查看任意用户的对话
    """
    try:
        # 权限检查
        if user_id is not None:
            if current_user.permission < 9 and user_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权查看其他用户的对话"
                )
        else:
            # 如果没有指定用户ID，普通用户只能看自己的对话
            if current_user.permission < 9:
                user_id = current_user.id
        
        skip = (page - 1) * size
        conversations = ConversationCRUD.get_conversations(db, user_id=user_id, skip=skip, limit=size)
        
        # 添加消息数量统计
        result = []
        for conv in conversations:
            conv_dict = conv.__dict__.copy()
            # 计算消息数量
            message_count = len(conv.messages) if hasattr(conv, 'messages') else 0
            conv_dict['message_count'] = message_count
            result.append(ConversationResponse(**conv_dict))
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话列表失败"
        )


@router.post(
    "/",
    response_model=ConversationResponse,
    summary="创建对话",
    description="""
    创建新的对话会话。

    **功能说明：**
    - 创建新的对话会话
    - 设置对话的基本信息和参数
    - 初始化对话状态
    - 返回创建的对话详细信息

    **权限控制：**
    - 普通用户只能为自己创建对话
    - 管理员可以为任意用户创建对话
    - user_id必须与当前用户ID匹配（非管理员）

    **必需字段：**
    - `user_id`: 对话所属用户ID
    - `title`: 对话标题

    **可选字段：**
    - `model_id`: 对话使用的模型ID
    - `temperature`: 对话温度设置
    - `system_prompt`: 系统提示词
    - `max_tokens`: 最大token限制

    **使用场景：**
    - 开始新的聊天会话
    - 创建特定主题的对话
    - 设置特定模型和参数的对话
    """,
    response_description="创建成功的对话详细信息"
)
async def create_conversation(
    conversation: ConversationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建对话

    创建新的对话会话，包括：
    - 设置对话基本信息
    - 配置模型和参数
    - 初始化对话状态

    权限控制：
    - 普通用户只能为自己创建对话
    - 管理员可以为任意用户创建对话
    """
    try:
        # 权限检查：只能为自己或管理员可以为任何人创建对话
        if current_user.permission < 9 and conversation.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只能为自己创建对话"
            )
        
        new_conversation = ConversationCRUD.create_conversation(db, conversation)
        return new_conversation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建对话失败"
        )


@router.get("/{conversation_id}", response_model=ConversationResponse, summary="获取指定对话")
async def get_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定对话"""
    try:
        conversation = ConversationCRUD.get_conversation(db, conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        # 权限检查
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该对话"
            )
        
        # 添加消息数量
        conv_dict = conversation.__dict__.copy()
        conv_dict['message_count'] = len(conversation.messages) if hasattr(conversation, 'messages') else 0
        
        return ConversationResponse(**conv_dict)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话信息失败"
        )


@router.put("/{conversation_id}", response_model=ConversationResponse, summary="更新对话")
async def update_conversation(
    conversation_id: int,
    conversation_update: ConversationUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新对话"""
    try:
        # 先获取对话进行权限检查
        conversation = ConversationCRUD.get_conversation(db, conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改该对话"
            )
        
        updated_conversation = ConversationCRUD.update_conversation(db, conversation_id, conversation_update)
        return updated_conversation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新对话失败"
        )


@router.delete("/{conversation_id}", response_model=SuccessResponse, summary="删除对话")
async def delete_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除对话"""
    try:
        # 先获取对话进行权限检查
        conversation = ConversationCRUD.get_conversation(db, conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该对话"
            )
        
        success = ConversationCRUD.delete_conversation(db, conversation_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        return SuccessResponse(message="对话删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除对话失败"
        )


@router.get("/{conversation_id}/stats", response_model=ConversationStatsResponse, summary="获取对话统计信息")
async def get_conversation_stats(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取对话统计信息"""
    try:
        # 先获取对话进行权限检查
        conversation = ConversationCRUD.get_conversation(db, conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权查看该对话统计信息"
            )
        
        stats = ConversationCRUD.get_conversation_stats(db, conversation_id)
        return stats
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation {conversation_id} stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话统计信息失败"
        )
