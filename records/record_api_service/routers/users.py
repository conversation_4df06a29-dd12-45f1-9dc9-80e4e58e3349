"""
用户相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from database import get_db
from auth import get_current_user, require_admin
from models import User
from schemas import (
    UserCreate, UserUpdate, UserResponse, UserStatsResponse,
    SuccessResponse, ErrorResponse, PaginationParams
)
from crud import UserCRUD

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/users", tags=["用户管理"])


@router.get(
    "/me",
    response_model=UserResponse,
    summary="获取当前用户信息",
    description="""
    获取当前认证用户的详细信息，包括个人设置和统计数据。

    **功能说明：**
    - 返回当前用户的完整个人信息
    - 包含用户权限级别和状态
    - 显示费用统计和余额信息
    - 提供当前会话设置

    **认证要求：**
    - 需要有效的API密钥认证
    - 返回与API密钥关联的用户信息

    **返回信息：**
    - 用户基本信息（ID、权限等级、状态）
    - 费用统计（总充值、总消费、当前余额）
    - Token使用统计（提示词和完成词数量）
    - 当前设置（模型、温度、对话等）

    **使用场景：**
    - 用户个人中心信息展示
    - 费用和使用量查询
    - 当前设置确认
    """,
    response_description="当前用户的详细信息和统计数据"
)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息

    返回当前认证用户的完整信息，包括：
    - 基本信息和权限
    - 费用统计和余额
    - Token使用统计
    - 当前设置和偏好
    """
    return current_user


@router.put(
    "/me",
    response_model=UserResponse,
    summary="更新当前用户信息",
    description="""
    更新当前认证用户的个人设置和偏好。

    **功能说明：**
    - 更新用户的个人设置和偏好
    - 支持部分字段更新
    - 保留未修改的字段不变
    - 返回更新后的完整用户信息

    **可更新字段：**
    - `is_active`: 用户状态
    - `mathjax`: 是否启用数学公式渲染
    - `current_model_id`: 当前默认模型ID
    - `current_temperature`: 当前默认温度设置
    - `current_conversation_id`: 当前活跃对话ID

    **认证要求：**
    - 需要有效的API密钥认证
    - 只能更新自己的信息

    **使用场景：**
    - 用户修改个人设置
    - 更新默认模型和参数
    - 切换当前活跃对话
    """,
    response_description="更新后的用户完整信息"
)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新当前用户信息

    更新当前认证用户的设置和偏好，包括：
    - 用户状态
    - 渲染偏好
    - 默认模型和参数
    - 当前活跃对话
    """
    try:
        updated_user = UserCRUD.update_user(db, current_user.id, user_update)
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        return updated_user
    except Exception as e:
        logger.error(f"Error updating current user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户信息失败"
        )


@router.get("/me/stats", response_model=UserStatsResponse, summary="获取当前用户统计信息")
async def get_current_user_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前用户统计信息"""
    try:
        stats = UserCRUD.get_user_stats(db, current_user.id)
        return stats
    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户统计信息失败"
        )


@router.get(
    "/",
    response_model=List[UserResponse],
    summary="获取用户列表",
    description="""
    获取系统中所有用户的列表，支持分页查询。仅限管理员访问。

    **功能说明：**
    - 返回系统中所有用户的信息
    - 支持分页查询，避免数据量过大
    - 包含每个用户的详细信息和统计数据

    **权限要求：**
    - 仅限管理员（权限级别 >= 9）访问
    - 需要有效的管理员API密钥

    **分页参数：**
    - `page`: 页码，从1开始
    - `size`: 每页大小，范围1-100

    **返回信息：**
    - 用户基本信息列表
    - 每个用户的费用统计
    - Token使用统计
    - 当前设置状态

    **使用场景：**
    - 管理员用户管理界面
    - 用户数据统计和分析
    - 系统用户监控
    """,
    response_description="分页的用户信息列表"
)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取用户列表（仅管理员）

    返回系统中所有用户的分页列表，包括：
    - 用户基本信息
    - 费用和使用统计
    - 当前设置状态
    """
    try:
        skip = (page - 1) * size
        users = UserCRUD.get_users(db, skip=skip, limit=size)
        return users
    except Exception as e:
        logger.error(f"Error getting users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.post(
    "/",
    response_model=UserResponse,
    summary="创建用户",
    description="""
    创建新用户账户，仅限管理员操作。

    **功能说明：**
    - 创建新的用户账户
    - 分配唯一的API密钥
    - 设置用户权限级别
    - 初始化用户设置和余额

    **权限要求：**
    - 仅限管理员（权限级别 >= 9）访问
    - 需要有效的管理员API密钥

    **必需字段：**
    - `api_key`: 用户的API密钥（必须唯一）
    - `permission`: 权限级别（1-9）

    **可选字段：**
    - `is_active`: 用户状态（默认true）
    - `mathjax`: 数学公式渲染（默认false）
    - `current_model_id`: 默认模型ID
    - `current_temperature`: 默认温度设置
    - `total_deposited`: 初始充值金额

    **验证规则：**
    - API密钥必须唯一
    - 权限级别必须在有效范围内
    - 金额字段必须为非负数

    **使用场景：**
    - 管理员创建新用户账户
    - 批量用户导入
    - 系统初始化用户设置
    """,
    response_description="创建成功的用户完整信息"
)
async def create_user(
    user: UserCreate,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    创建用户（仅管理员）

    创建新的用户账户，包括：
    - 分配唯一API密钥
    - 设置权限级别
    - 初始化用户设置
    - 设置初始余额
    """
    try:
        # 检查API密钥是否已存在
        existing_user = UserCRUD.get_user_by_api_key(db, user.api_key)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="API密钥已存在"
            )
        
        new_user = UserCRUD.create_user(db, user)
        return new_user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )


@router.get("/{user_id}", response_model=UserResponse, summary="获取指定用户信息")
async def get_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定用户信息"""
    try:
        # 权限检查
        if current_user.permission < 9 and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该用户的数据"
            )

        user = UserCRUD.get_user(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        return user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.put("/{user_id}", response_model=UserResponse, summary="更新指定用户信息")
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新指定用户信息"""
    try:
        # 权限检查
        if current_user.permission < 9 and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改该用户的数据"
            )

        updated_user = UserCRUD.update_user(db, user_id, user_update)
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        return updated_user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户信息失败"
        )


@router.delete("/{user_id}", response_model=SuccessResponse, summary="删除用户")
async def delete_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """删除用户（仅管理员）"""
    try:
        # 不能删除自己
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己的账户"
            )
        
        success = UserCRUD.delete_user(db, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return SuccessResponse(message="用户删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )


@router.get("/{user_id}/stats", response_model=UserStatsResponse, summary="获取指定用户统计信息")
async def get_user_stats(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定用户统计信息"""
    try:
        # 权限检查
        if current_user.permission < 9 and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权查看该用户的统计信息"
            )

        stats = UserCRUD.get_user_stats(db, user_id)
        return stats
    except Exception as e:
        logger.error(f"Error getting user {user_id} stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户统计信息失败"
        )
