"""
消息相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from database import get_db
from auth import get_current_user, check_conversation_access
from models import User, MessageRole
from schemas import (
    MessageCreate, MessageUpdate, MessageResponse, MessageWithCostResponse,
    MessageCostCreate, MessageCostUpdate, MessageCostResponse,
    SuccessResponse
)
from crud import MessageCRUD, MessageCostCRUD, ConversationCRUD

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/messages", tags=["消息管理"])


@router.get(
    "/",
    response_model=List[MessageWithCostResponse],
    summary="获取消息列表",
    description="""
    获取消息列表，支持按对话和角色筛选，包含费用信息。

    **功能说明：**
    - 获取用户有权访问的消息列表
    - 支持按对话ID和消息角色筛选
    - 包含消息的费用统计信息
    - 支持分页查询，避免数据量过大

    **权限控制：**
    - 用户只能查看自己有权访问的对话中的消息
    - 管理员可以查看所有消息
    - 如果指定对话ID，会验证对话访问权限

    **筛选参数：**
    - `conversation_id`: 对话ID（可选）
    - `role`: 消息角色（user/assistant/system）（可选）

    **分页参数：**
    - `page`: 页码，从1开始
    - `size`: 每页大小，范围1-200

    **返回信息：**
    - 消息基本信息（ID、内容、角色、时间等）
    - 费用信息（仅assistant消息有费用）
    - Token使用统计
    - 渲染格式信息

    **使用场景：**
    - 对话历史查看
    - 消息搜索和筛选
    - 费用统计和分析
    - 对话内容导出
    """,
    response_description="包含费用信息的消息列表"
)
async def get_messages(
    conversation_id: Optional[int] = Query(None, description="对话ID"),
    role: Optional[MessageRole] = Query(None, description="消息角色"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=200, description="每页大小"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取消息列表

    返回用户有权访问的消息列表，包括：
    - 消息基本信息和内容
    - 费用统计信息（assistant消息）
    - Token使用统计
    - 渲染格式信息

    支持按对话和角色筛选，包含完整的权限控制。
    """
    try:
        # 如果指定了对话ID，需要检查权限
        if conversation_id:
            conversation = ConversationCRUD.get_conversation(db, conversation_id)
            if not conversation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话不存在"
                )
            
            if not check_conversation_access(conversation.user_id, current_user):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问该对话的消息"
                )
        
        skip = (page - 1) * size
        messages = MessageCRUD.get_messages(
            db, 
            conversation_id=conversation_id, 
            role=role, 
            skip=skip, 
            limit=size
        )
        
        # 转换为带费用的响应格式
        result = []
        for message in messages:
            message_response = MessageWithCostResponse.model_validate(message)
            result.append(message_response)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting messages: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息列表失败"
        )


@router.post(
    "/",
    response_model=MessageWithCostResponse,
    summary="创建消息",
    description="""
    在指定对话中创建新消息，支持用户和助手消息。

    **功能说明：**
    - 在指定对话中添加新消息
    - 支持用户消息、助手消息和系统消息
    - 自动处理助手消息的费用计算
    - 支持多种内容格式（文本、HTML、KaTeX）

    **权限控制：**
    - 用户只能在自己有权访问的对话中创建消息
    - 管理员可以在任意对话中创建消息
    - 会验证对话存在性和访问权限

    **必需字段：**
    - `conversation_id`: 对话ID
    - `role`: 消息角色（user/assistant/system）
    - `content`: 消息内容

    **可选字段：**
    - `content_url`: 内容存储URL（用于大内容）
    - `katex_content`: KaTeX格式内容
    - `katex_content_url`: KaTeX内容存储URL
    - `prompt_tokens`: 提示词token数（assistant消息）
    - `completion_tokens`: 完成词token数（assistant消息）
    - `total_cost`: 总费用（assistant消息）

    **费用处理：**
    - 仅assistant消息会产生费用
    - 自动创建费用记录
    - 更新用户余额和统计

    **使用场景：**
    - 用户发送消息
    - 助手回复消息
    - 系统提示消息
    - 对话内容记录
    """,
    response_description="创建成功的消息信息，包含费用数据"
)
async def create_message(
    message: MessageCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建消息

    在指定对话中创建新消息，包括：
    - 验证对话权限
    - 创建消息记录
    - 处理费用计算（assistant消息）
    - 更新用户统计

    支持多种消息类型和内容格式。
    """
    try:
        # 检查对话权限
        conversation = ConversationCRUD.get_conversation(db, message.conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权在该对话中创建消息"
            )
        
        new_message = MessageCRUD.create_message(db, message)
        
        # 如果是assistant消息且提供了费用信息，创建费用记录
        if (message.role == MessageRole.ASSISTANT and 
            hasattr(message, 'prompt_tokens') and 
            hasattr(message, 'completion_tokens')):
            # 这里可以添加费用计算逻辑
            pass
        
        return MessageWithCostResponse.model_validate(new_message)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建消息失败"
        )


@router.get("/{message_id}", response_model=MessageWithCostResponse, summary="获取指定消息")
async def get_message(
    message_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定消息"""
    try:
        message = MessageCRUD.get_message(db, message_id)
        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        # 检查权限
        conversation = ConversationCRUD.get_conversation(db, message.conversation_id)
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该消息"
            )
        
        return MessageWithCostResponse.model_validate(message)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting message {message_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息失败"
        )


@router.put("/{message_id}", response_model=MessageWithCostResponse, summary="更新消息")
async def update_message(
    message_id: int,
    message_update: MessageUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新消息"""
    try:
        # 先获取消息进行权限检查
        message = MessageCRUD.get_message(db, message_id)
        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        conversation = ConversationCRUD.get_conversation(db, message.conversation_id)
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改该消息"
            )
        
        updated_message = MessageCRUD.update_message(db, message_id, message_update)

        return MessageWithCostResponse.model_validate(updated_message)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating message {message_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新消息失败"
        )


@router.delete("/{message_id}", response_model=SuccessResponse, summary="删除消息")
async def delete_message(
    message_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除消息"""
    try:
        # 先获取消息进行权限检查
        message = MessageCRUD.get_message(db, message_id)
        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        conversation = ConversationCRUD.get_conversation(db, message.conversation_id)
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该消息"
            )
        
        success = MessageCRUD.delete_message(db, message_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        return SuccessResponse(message="消息删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting message {message_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除消息失败"
        )


# 消息费用相关路由
@router.post("/{message_id}/cost", response_model=MessageCostResponse, summary="创建消息费用记录")
async def create_message_cost(
    message_id: int,
    cost: MessageCostCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建消息费用记录"""
    try:
        # 检查消息权限
        message = MessageCRUD.get_message(db, message_id)
        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        conversation = ConversationCRUD.get_conversation(db, message.conversation_id)
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权为该消息创建费用记录"
            )
        
        # 设置正确的message_id
        cost.message_id = message_id
        
        new_cost = MessageCostCRUD.create_message_cost(db, cost)
        return new_cost
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating message cost for message {message_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建消息费用记录失败"
        )


@router.put("/{message_id}/cost", response_model=MessageCostResponse, summary="更新消息费用记录")
async def update_message_cost(
    message_id: int,
    cost_update: MessageCostUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新消息费用记录"""
    try:
        # 检查消息权限
        message = MessageCRUD.get_message(db, message_id)
        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息不存在"
            )
        
        conversation = ConversationCRUD.get_conversation(db, message.conversation_id)
        if not check_conversation_access(conversation.user_id, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改该消息的费用记录"
            )
        
        updated_cost = MessageCostCRUD.update_message_cost(db, message_id, cost_update)
        if not updated_cost:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="消息费用记录不存在"
            )
        
        return updated_cost
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating message cost for message {message_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新消息费用记录失败"
        )
