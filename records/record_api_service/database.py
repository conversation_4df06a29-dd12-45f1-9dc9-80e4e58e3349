"""
数据库连接和会话管理
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from typing import Generator
import logging
from config import DATABASE_CONFIG

logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    DATABASE_CONFIG['url'],
    echo=DATABASE_CONFIG['echo'],
    pool_size=DATABASE_CONFIG['pool_size'],
    max_overflow=DATABASE_CONFIG['max_overflow'],
    pool_pre_ping=DATABASE_CONFIG['pool_pre_ping'],
    pool_recycle=DATABASE_CONFIG['pool_recycle'],
    poolclass=QueuePool,
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    用于FastAPI依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


@contextmanager
def get_db_session():
    """
    获取数据库会话的上下文管理器
    用于非FastAPI环境
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def init_database():
    """
    初始化数据库
    创建所有表
    """
    try:
        from models import Base
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


def check_database_connection():
    """
    检查数据库连接
    """
    import time
    max_retries = 5
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            from sqlalchemy import text
            with engine.connect() as connection:
                result = connection.execute(text("SELECT 1"))
                result.fetchone()
            logger.info("Database connection successful")
            return True
        except Exception as e:
            logger.warning(f"Database connection attempt {attempt + 1}/{max_retries} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                logger.error(f"Database connection failed after {max_retries} attempts")
                return False


def get_database_info():
    """
    获取数据库信息
    """
    try:
        from sqlalchemy import text
        with engine.connect() as connection:
            result = connection.execute(text("SELECT VERSION() as version"))
            version = result.fetchone()[0]

            result = connection.execute(text("SELECT DATABASE() as database"))
            database = result.fetchone()[0]

            return {
                "version": version,
                "database": database,
                "url": DATABASE_CONFIG['url'].split('@')[1] if '@' in DATABASE_CONFIG['url'] else DATABASE_CONFIG['url']
            }
    except Exception as e:
        logger.error(f"Failed to get database info: {e}")
        return None
