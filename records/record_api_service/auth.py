"""
认证和权限管理
"""
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional
import logging
from database import get_db
from models import User

logger = logging.getLogger(__name__)

# HTTP Bearer认证
security = HTTPBearer()


class AuthenticationError(HTTPException):
    """认证错误"""
    def __init__(self, detail: str = "认证失败"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


class PermissionError(HTTPException):
    """权限错误"""
    def __init__(self, detail: str = "权限不足"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
        )


def get_user_by_api_key(db: Session, api_key: str) -> Optional[User]:
    """
    根据API密钥获取用户
    """
    try:
        user = db.query(User).filter(User.api_key == api_key).first()
        return user
    except Exception as e:
        logger.error(f"Error getting user by API key: {e}")
        return None


def authenticate_user(api_key: str, db: Session) -> User:
    """
    认证用户
    """
    if not api_key:
        raise AuthenticationError("API密钥不能为空")
    
    user = get_user_by_api_key(db, api_key)
    if not user:
        raise AuthenticationError("无效的API密钥")
    
    if not user.is_active:
        raise AuthenticationError("用户账户已被禁用")
    
    return user


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    获取当前用户
    用于FastAPI依赖注入
    """
    api_key = credentials.credentials
    return authenticate_user(api_key, db)


def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前活跃用户
    """
    if not current_user.is_active:
        raise AuthenticationError("用户账户已被禁用")
    return current_user


def require_permission(min_permission: int):
    """
    权限检查装饰器
    """
    def permission_checker(current_user: User = Depends(get_current_active_user)) -> User:
        if current_user.permission < min_permission:
            raise PermissionError(f"需要权限级别 {min_permission} 或更高")
        return current_user
    
    return permission_checker


def require_admin(current_user: User = Depends(get_current_active_user)) -> User:
    """
    需要管理员权限
    """
    if current_user.permission < 9:
        raise PermissionError("需要管理员权限")
    return current_user


def check_user_access(target_user_id: int, current_user: User) -> bool:
    """
    检查用户访问权限
    用户只能访问自己的数据，管理员可以访问所有数据
    """
    if current_user.permission >= 9:  # 管理员
        return True
    
    if current_user.id == target_user_id:  # 用户自己
        return True
    
    return False


def require_user_access(target_user_id: int):
    """
    需要用户访问权限
    """
    def access_checker(current_user: User = Depends(get_current_active_user)) -> User:
        if not check_user_access(target_user_id, current_user):
            raise PermissionError("无权访问该用户的数据")
        return current_user

    return access_checker


def check_conversation_access(conversation_user_id: int, current_user: User) -> bool:
    """
    检查对话访问权限
    """
    return check_user_access(conversation_user_id, current_user)


def require_conversation_access(conversation_user_id: int):
    """
    需要对话访问权限
    """
    def access_checker(current_user: User = Depends(get_current_active_user)) -> User:
        if not check_conversation_access(conversation_user_id, current_user):
            raise PermissionError("无权访问该对话")
        return current_user
    
    return access_checker
