"""
配置文件
"""
import os
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """应用配置"""
    
    # 数据库配置
    database_url: str = "mysql+pymysql://records_user:records_password@localhost:3307/vdb_records"
    
    # API配置
    api_secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 应用配置
    app_name: str = "Record FastAPI Service"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 6002
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/record-fastapi-service.log"
    
    # CORS配置
    cors_origins: list = ["*"]
    cors_methods: list = ["*"]
    cors_headers: list = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # 忽略额外的环境变量


# 创建配置实例
settings = Settings()


# 数据库配置
DATABASE_CONFIG = {
    'url': settings.database_url,
    'echo': settings.debug,  # 在调试模式下显示SQL语句
    'pool_size': 10,
    'max_overflow': 20,
    'pool_pre_ping': True,
    'pool_recycle': 3600,
}


# API文档配置
API_DOCS_CONFIG = {
    'title': settings.app_name,
    'description': """
    # Record FastAPI Service

    基于FastAPI的聊天记录数据库API服务，为聊天应用提供完整的数据管理功能。

    ## 🚀 核心功能

    - **用户管理**: 用户认证、信息管理、权限控制
    - **对话管理**: 对话创建、查询、更新、删除
    - **消息管理**: 消息发送、查询、按时间排序
    - **费用统计**: 独立的费用记录和统计分析
    - **权限控制**: 基于API Key的多级权限管理
    - **高性能**: FastAPI异步处理，数据库连接池

    ## 🗄️ 数据库架构

    ### 核心表结构

    1. **users** - 用户信息和权限管理
    2. **conversations** - 对话记录和设置
    3. **messages** - 统一消息表（用户+助手）
    4. **message_costs** - 费用记录（仅助手消息）

    ### 设计特点

    - ✅ 消息按时间统一排序（解决分表查询复杂性）
    - ✅ 内容存储支持URL引用，支持大内容
    - ✅ 支持KaTeX和纯HTML两种渲染格式
    - ✅ 只有assistant消息产生费用
    - ✅ 完整的外键约束和数据完整性

    ## 🔐 认证方式

    所有API请求都需要在请求头中包含有效的API密钥：

    ```
    Authorization: Bearer your-api-key
    ```

    ### 权限级别

    - **级别 1-8**: 普通用户，只能访问自己的数据
    - **级别 9**: 管理员，可以访问所有数据和管理功能

    ## 📋 API端点分组

    - **系统监控**: 健康检查和服务状态
    - **系统信息**: 服务信息和配置
    - **用户管理**: 用户CRUD和统计
    - **对话管理**: 对话CRUD和查询
    - **消息管理**: 消息CRUD和费用统计

    ## 🔗 相关服务

    - **model-api-service**: OpenAI兼容的模型API服务
    - **records-database**: MySQL数据存储
    """,
    'version': settings.app_version,
    'contact': {
        'name': 'Record FastAPI Service',
        'email': '<EMAIL>',
    },
    'license_info': {
        'name': 'MIT License',
        'url': 'https://opensource.org/licenses/MIT',
    },
    'tags_metadata': [
        {
            'name': '系统监控',
            'description': '服务健康检查和状态监控相关接口'
        },
        {
            'name': '系统信息',
            'description': '服务信息查询和配置相关接口'
        },
        {
            'name': '用户管理',
            'description': '用户账户管理、认证和统计相关接口'
        },
        {
            'name': '对话管理',
            'description': '对话会话的创建、查询、更新和删除接口'
        },
        {
            'name': '消息管理',
            'description': '消息的发送、查询和费用统计相关接口'
        }
    ]
}


# 日志配置
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        },
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': settings.log_level,
            'formatter': 'default',
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': settings.log_level,
            'formatter': 'detailed',
            'filename': settings.log_file,
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
        },
    },
    'loggers': {
        '': {
            'level': settings.log_level,
            'handlers': ['console', 'file'],
        },
        'uvicorn': {
            'level': settings.log_level,
            'handlers': ['console', 'file'],
            'propagate': False,
        },
        'sqlalchemy.engine': {
            'level': 'INFO' if settings.debug else 'WARNING',
            'handlers': ['console', 'file'],
            'propagate': False,
        },
    },
}
