"""
CRUD操作
"""
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, and_
from typing import List, Optional, Tuple
from datetime import datetime
import logging

from models import User, Conversation, Message, MessageCost, MessageRole
from schemas import (
    UserCreate, UserUpdate, ConversationCreate, ConversationUpdate,
    MessageCreate, MessageUpdate, MessageCostCreate, MessageCostUpdate
)

logger = logging.getLogger(__name__)


# 用户CRUD操作
class UserCRUD:
    @staticmethod
    def get_user(db: Session, user_id: int) -> Optional[User]:
        """获取用户"""
        return db.query(User).filter(User.id == user_id).first()
    
    @staticmethod
    def get_user_by_api_key(db: Session, api_key: str) -> Optional[User]:
        """根据API密钥获取用户"""
        return db.query(User).filter(User.api_key == api_key).first()
    
    @staticmethod
    def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表"""
        return db.query(User).offset(skip).limit(limit).all()
    
    @staticmethod
    def create_user(db: Session, user: UserCreate) -> User:
        """创建用户"""
        db_user = User(**user.model_dump())
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user
    
    @staticmethod
    def update_user(db: Session, user_id: int, user_update: UserUpdate) -> Optional[User]:
        """更新用户"""
        db_user = db.query(User).filter(User.id == user_id).first()
        if not db_user:
            return None
        
        update_data = user_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_user, field, value)
        
        db.commit()
        db.refresh(db_user)
        return db_user
    
    @staticmethod
    def delete_user(db: Session, user_id: int) -> bool:
        """删除用户"""
        db_user = db.query(User).filter(User.id == user_id).first()
        if not db_user:
            return False
        
        db.delete(db_user)
        db.commit()
        return True
    
    @staticmethod
    def get_user_stats(db: Session, user_id: int) -> dict:
        """获取用户统计信息"""
        # 基础统计
        conversation_count = db.query(func.count(Conversation.id)).filter(
            Conversation.user_id == user_id
        ).scalar()
        
        message_count = db.query(func.count(Message.id)).join(Conversation).filter(
            Conversation.user_id == user_id
        ).scalar()
        
        user_message_count = db.query(func.count(Message.id)).join(Conversation).filter(
            and_(Conversation.user_id == user_id, Message.role == MessageRole.USER)
        ).scalar()
        
        assistant_message_count = db.query(func.count(Message.id)).join(Conversation).filter(
            and_(Conversation.user_id == user_id, Message.role == MessageRole.ASSISTANT)
        ).scalar()
        
        # 费用统计
        cost_stats = db.query(
            func.sum(MessageCost.total_cost).label('total_cost'),
            func.sum(MessageCost.prompt_tokens).label('total_prompt_tokens'),
            func.sum(MessageCost.completion_tokens).label('total_completion_tokens')
        ).join(Message).join(Conversation).filter(
            Conversation.user_id == user_id
        ).first()
        
        return {
            'user_id': user_id,
            'total_conversations': conversation_count or 0,
            'total_messages': message_count or 0,
            'total_user_messages': user_message_count or 0,
            'total_assistant_messages': assistant_message_count or 0,
            'total_cost': cost_stats.total_cost or 0,
            'total_prompt_tokens': cost_stats.total_prompt_tokens or 0,
            'total_completion_tokens': cost_stats.total_completion_tokens or 0,
        }


# 对话CRUD操作
class ConversationCRUD:
    @staticmethod
    def get_conversation(db: Session, conversation_id: int) -> Optional[Conversation]:
        """获取对话"""
        return db.query(Conversation).filter(Conversation.id == conversation_id).first()
    
    @staticmethod
    def get_conversations(
        db: Session, 
        user_id: Optional[int] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[Conversation]:
        """获取对话列表"""
        query = db.query(Conversation)
        if user_id:
            query = query.filter(Conversation.user_id == user_id)
        return query.order_by(desc(Conversation.latest_revised_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def create_conversation(db: Session, conversation: ConversationCreate) -> Conversation:
        """创建对话"""
        db_conversation = Conversation(**conversation.model_dump())
        db.add(db_conversation)
        db.commit()
        db.refresh(db_conversation)
        return db_conversation
    
    @staticmethod
    def update_conversation(
        db: Session, 
        conversation_id: int, 
        conversation_update: ConversationUpdate
    ) -> Optional[Conversation]:
        """更新对话"""
        db_conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
        if not db_conversation:
            return None
        
        update_data = conversation_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_conversation, field, value)
        
        # 更新最后修改时间
        db_conversation.latest_revised_at = datetime.now()
        
        db.commit()
        db.refresh(db_conversation)
        return db_conversation
    
    @staticmethod
    def delete_conversation(db: Session, conversation_id: int) -> bool:
        """删除对话"""
        db_conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
        if not db_conversation:
            return False
        
        db.delete(db_conversation)
        db.commit()
        return True
    
    @staticmethod
    def get_conversation_stats(db: Session, conversation_id: int) -> dict:
        """获取对话统计信息"""
        message_count = db.query(func.count(Message.id)).filter(
            Message.conversation_id == conversation_id
        ).scalar()
        
        user_message_count = db.query(func.count(Message.id)).filter(
            and_(Message.conversation_id == conversation_id, Message.role == MessageRole.USER)
        ).scalar()
        
        assistant_message_count = db.query(func.count(Message.id)).filter(
            and_(Message.conversation_id == conversation_id, Message.role == MessageRole.ASSISTANT)
        ).scalar()
        
        total_cost = db.query(func.sum(MessageCost.total_cost)).join(Message).filter(
            Message.conversation_id == conversation_id
        ).scalar()
        
        return {
            'conversation_id': conversation_id,
            'total_messages': message_count or 0,
            'user_messages': user_message_count or 0,
            'assistant_messages': assistant_message_count or 0,
            'total_cost': total_cost or 0,
        }


# 消息CRUD操作
class MessageCRUD:
    @staticmethod
    def get_message(db: Session, message_id: int) -> Optional[Message]:
        """获取消息"""
        return db.query(Message).options(joinedload(Message.cost)).filter(Message.id == message_id).first()

    @staticmethod
    def get_messages(
        db: Session,
        conversation_id: Optional[int] = None,
        role: Optional[MessageRole] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Message]:
        """获取消息列表"""
        query = db.query(Message).options(joinedload(Message.cost))

        if conversation_id:
            query = query.filter(Message.conversation_id == conversation_id)
        if role:
            query = query.filter(Message.role == role)

        return query.order_by(Message.created_at).offset(skip).limit(limit).all()

    @staticmethod
    def create_message(db: Session, message: MessageCreate) -> Message:
        """创建消息"""
        db_message = Message(**message.model_dump())
        db.add(db_message)
        db.commit()
        db.refresh(db_message)

        # 更新对话的最后修改时间
        db.query(Conversation).filter(Conversation.id == message.conversation_id).update({
            'latest_revised_at': datetime.now()
        })
        db.commit()

        return db_message

    @staticmethod
    def update_message(db: Session, message_id: int, message_update: MessageUpdate) -> Optional[Message]:
        """更新消息"""
        db_message = db.query(Message).filter(Message.id == message_id).first()
        if not db_message:
            return None

        update_data = message_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_message, field, value)

        db.commit()
        db.refresh(db_message)
        return db_message

    @staticmethod
    def delete_message(db: Session, message_id: int) -> bool:
        """删除消息"""
        db_message = db.query(Message).filter(Message.id == message_id).first()
        if not db_message:
            return False

        conversation_id = db_message.conversation_id
        db.delete(db_message)

        # 更新对话的最后修改时间
        db.query(Conversation).filter(Conversation.id == conversation_id).update({
            'latest_revised_at': datetime.now()
        })

        db.commit()
        return True


# 消息费用CRUD操作
class MessageCostCRUD:
    @staticmethod
    def get_message_cost(db: Session, message_id: int) -> Optional[MessageCost]:
        """获取消息费用"""
        return db.query(MessageCost).filter(MessageCost.message_id == message_id).first()

    @staticmethod
    def create_message_cost(db: Session, cost: MessageCostCreate) -> MessageCost:
        """创建消息费用"""
        # 检查消息是否为assistant类型
        message = db.query(Message).filter(Message.id == cost.message_id).first()
        if not message or message.role != MessageRole.ASSISTANT:
            raise ValueError("只能为assistant消息创建费用记录")

        db_cost = MessageCost(**cost.model_dump())
        db.add(db_cost)
        db.commit()
        db.refresh(db_cost)
        return db_cost

    @staticmethod
    def update_message_cost(
        db: Session,
        message_id: int,
        cost_update: MessageCostUpdate
    ) -> Optional[MessageCost]:
        """更新消息费用"""
        db_cost = db.query(MessageCost).filter(MessageCost.message_id == message_id).first()
        if not db_cost:
            return None

        update_data = cost_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_cost, field, value)

        db.commit()
        db.refresh(db_cost)
        return db_cost

    @staticmethod
    def delete_message_cost(db: Session, message_id: int) -> bool:
        """删除消息费用"""
        db_cost = db.query(MessageCost).filter(MessageCost.message_id == message_id).first()
        if not db_cost:
            return False

        db.delete(db_cost)
        db.commit()
        return True
