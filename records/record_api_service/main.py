"""
Record FastAPI Service - 主应用文件
"""
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging.config
import uvicorn

from config import settings, API_DOCS_CONFIG, LOGGING_CONFIG
from database import init_database, check_database_connection, get_database_info
from routers import users, conversations, messages


# 配置日志
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Starting Record FastAPI Service...")
    
    # 检查数据库连接
    if not check_database_connection():
        logger.error("Database connection failed! Service will start but database features may not work.")
    else:
        # 初始化数据库表
        try:
            init_database()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            logger.warning("Service will start but database features may not work.")
    
    logger.info("Record FastAPI Service started successfully")
    
    yield
    
    # 关闭时执行
    logger.info("Shutting down Record FastAPI Service...")


# 创建FastAPI应用
app = FastAPI(
    lifespan=lifespan,
    **API_DOCS_CONFIG
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "error_code": "INTERNAL_SERVER_ERROR"
        }
    )


# 健康检查
@app.get(
    "/health",
    tags=["系统监控"],
    summary="服务健康检查",
    description="""
    检查Record FastAPI Service的运行状态和数据库连接状态。

    **功能说明：**
    - 检查服务是否正常运行
    - 验证数据库连接状态
    - 返回服务基本信息和版本
    - 提供数据库连接详情

    **返回状态：**
    - `healthy`: 服务和数据库都正常
    - `unhealthy`: 服务或数据库存在问题

    **使用场景：**
    - 监控系统健康状态
    - 负载均衡器健康检查
    - 服务部署验证
    - 运维监控告警
    """,
    response_description="服务健康状态和数据库连接信息"
)
async def health_check():
    """
    服务健康检查

    检查Record FastAPI Service的整体健康状态，包括：
    - 服务运行状态
    - 数据库连接状态
    - 服务版本信息
    - 数据库版本和连接详情
    """
    try:
        db_connected = check_database_connection()
        db_info = get_database_info()
        
        return {
            "status": "healthy" if db_connected else "unhealthy",
            "service": settings.app_name,
            "version": settings.app_version,
            "database": {
                "connected": db_connected,
                "info": db_info
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": settings.app_name,
                "version": settings.app_version,
                "error": str(e)
            }
        )


# 根路径
@app.get(
    "/",
    tags=["系统信息"],
    summary="服务根路径",
    description="""
    Record FastAPI Service的根路径接口，提供服务基本信息。

    **功能说明：**
    - 返回服务名称和版本
    - 提供API文档链接
    - 确认服务运行状态

    **使用场景：**
    - 快速确认服务可访问性
    - 获取API文档入口
    - 服务发现和注册
    """,
    response_description="服务基本信息和文档链接"
)
async def root():
    """
    服务根路径

    返回Record FastAPI Service的基本信息，包括：
    - 服务名称和版本
    - 运行状态
    - API文档链接
    """
    return {
        "service": settings.app_name,
        "version": settings.app_version,
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc"
    }


# 服务信息
@app.get(
    "/info",
    tags=["系统信息"],
    summary="详细服务信息",
    description="""
    获取Record FastAPI Service的详细信息，包括配置和功能特性。

    **功能说明：**
    - 返回完整的服务配置信息
    - 提供数据库连接详情
    - 列出所有支持的功能特性
    - 显示服务运行参数

    **包含信息：**
    - 服务基本信息（名称、版本、调试模式等）
    - 数据库连接信息
    - 功能特性列表
    - 网络配置信息

    **使用场景：**
    - 服务配置验证
    - 运维信息收集
    - 功能特性确认
    - 故障排查支持
    """,
    response_description="详细的服务配置和功能信息"
)
async def service_info():
    """
    获取详细服务信息

    返回Record FastAPI Service的完整配置信息，包括：
    - 服务配置（名称、版本、网络设置等）
    - 数据库连接信息
    - 支持的功能特性列表
    """
    try:
        db_info = get_database_info()
        return {
            "service": {
                "name": settings.app_name,
                "version": settings.app_version,
                "debug": settings.debug,
                "host": settings.host,
                "port": settings.port
            },
            "database": db_info,
            "features": [
                "用户管理",
                "对话管理", 
                "消息管理",
                "费用统计",
                "权限控制"
            ]
        }
    except Exception as e:
        logger.error(f"Error getting service info: {e}")
        raise HTTPException(status_code=500, detail="获取服务信息失败")


# 注册路由
app.include_router(users.router, prefix="/api/v1")
app.include_router(conversations.router, prefix="/api/v1")
app.include_router(messages.router, prefix="/api/v1")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
