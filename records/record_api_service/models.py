"""
数据库模型定义
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, DECIMAL, ForeignKey, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional
import enum


Base = declarative_base()


class MessageRole(str, enum.Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"


class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    api_key = Column(String(255), unique=True, nullable=False, index=True)
    created_at = Column(DateTime, default=func.now())
    is_active = Column(Boolean, default=True, index=True)
    permission = Column(Integer, default=1, nullable=False, index=True)
    mathjax = Column(Boolean, default=False)
    current_model_id = Column(Integer)
    current_temperature = Column(DECIMAL(3, 2), default=0.70)
    current_conversation_id = Column(Integer, ForeignKey("conversations.id", ondelete="SET NULL"))
    total_deposited = Column(DECIMAL(10, 4), default=0.0000)
    total_spent = Column(DECIMAL(10, 4), default=0.0000)
    current_balance = Column(DECIMAL(10, 4), default=0.0000)
    total_prompt_tokens = Column(Integer, default=0)
    total_completion_tokens = Column(Integer, default=0)
    
    # 关系
    conversations = relationship("Conversation", back_populates="user", foreign_keys="Conversation.user_id")
    current_conversation = relationship("Conversation", foreign_keys=[current_conversation_id])


class Conversation(Base):
    """对话表"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime, default=func.now(), index=True)
    latest_revised_at = Column(DateTime, default=func.now(), onupdate=func.now(), index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    title = Column(String(255))
    
    # 关系
    user = relationship("User", back_populates="conversations", foreign_keys=[user_id])
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")


class Message(Base):
    """消息表"""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False, index=True)
    role = Column(Enum(MessageRole), nullable=False, index=True)
    created_at = Column(DateTime, default=func.now(), index=True)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    original_url = Column(String(500), nullable=False, comment="消息内容存储在MinIO的URL")
    rendered_katex_url = Column(String(500), comment="渲染后的HTML版本URL（包含KaTeX）")
    rendered_plain_url = Column(String(500), comment="渲染后的HTML版本URL（不含KaTeX）")
    
    # 以下字段仅对assistant消息有效
    model_id = Column(Integer, comment="使用的模型ID（仅assistant消息）", index=True)
    temperature = Column(DECIMAL(3, 2), comment="温度参数（仅assistant消息）")
    max_tokens = Column(Integer, comment="最大token数（仅assistant消息）")
    is_error = Column(Boolean, default=False, comment="是否出错（仅assistant消息）", index=True)
    error_info = Column(Text, comment="错误信息（仅assistant消息）")
    
    # 关系
    conversation = relationship("Conversation", back_populates="messages")
    cost = relationship("MessageCost", back_populates="message", uselist=False, cascade="all, delete-orphan")


class MessageCost(Base):
    """消息费用表"""
    __tablename__ = "message_costs"
    
    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("messages.id", ondelete="CASCADE"), nullable=False, unique=True, index=True)
    prompt_tokens = Column(Integer, default=0)
    completion_tokens = Column(Integer, default=0)
    prompt_cost = Column(DECIMAL(10, 6), default=0.000000)
    completion_cost = Column(DECIMAL(10, 6), default=0.000000)
    total_cost = Column(DECIMAL(10, 6), default=0.000000, index=True)
    created_at = Column(DateTime, default=func.now(), index=True)
    
    # 关系
    message = relationship("Message", back_populates="cost")


class SystemInfo(Base):
    """系统信息表"""
    __tablename__ = "system_info"
    
    id = Column(Integer, primary_key=True, index=True)
    version = Column(String(20), nullable=False)
    initialized_at = Column(DateTime, default=func.now())
    notes = Column(Text)
