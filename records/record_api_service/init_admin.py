"""
初始化管理员用户脚本
"""
import asyncio
from sqlalchemy.orm import Session
from database import get_db_session, init_database
from models import User
from schemas import UserCreate
from crud import UserCRUD
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_admin_user():
    """创建管理员用户"""
    try:
        # 初始化数据库
        init_database()
        logger.info("Database initialized")
        
        with get_db_session() as db:
            # 检查管理员是否已存在
            admin_api_key = "admin-api-key-change-in-production"
            existing_admin = UserCRUD.get_user_by_api_key(db, admin_api_key)
            
            if existing_admin:
                logger.info(f"Admin user already exists with ID: {existing_admin.id}")
                # 刷新对象以避免会话问题
                db.refresh(existing_admin)
                return existing_admin
            
            # 创建管理员用户
            admin_data = UserCreate(
                api_key=admin_api_key,
                is_active=True,
                permission=9,  # 管理员权限
                mathjax=False,
                current_model_id=None,
                current_temperature=0.70
            )
            
            admin_user = UserCRUD.create_user(db, admin_data)
            logger.info(f"Admin user created with ID: {admin_user.id}")
            
            return admin_user
            
    except Exception as e:
        logger.error(f"Error creating admin user: {e}")
        raise


def create_test_users():
    """创建测试用户"""
    try:
        with get_db_session() as db:
            test_users = [
                {
                    "api_key": "test-user-001",
                    "is_active": True,
                    "permission": 1,
                    "mathjax": False,
                    "current_temperature": 0.70
                },
                {
                    "api_key": "test-user-002", 
                    "is_active": True,
                    "permission": 1,
                    "mathjax": True,
                    "current_temperature": 0.80
                }
            ]
            
            created_users = []
            for user_data in test_users:
                # 检查用户是否已存在
                existing_user = UserCRUD.get_user_by_api_key(db, user_data["api_key"])
                if existing_user:
                    logger.info(f"Test user {user_data['api_key']} already exists")
                    db.refresh(existing_user)
                    created_users.append(existing_user)
                    continue
                
                # 创建测试用户
                user_create = UserCreate(**user_data)
                new_user = UserCRUD.create_user(db, user_create)
                logger.info(f"Test user created: {new_user.api_key} (ID: {new_user.id})")
                created_users.append(new_user)
            
            return created_users
            
    except Exception as e:
        logger.error(f"Error creating test users: {e}")
        raise


def main():
    """主函数"""
    logger.info("Starting admin initialization...")
    
    try:
        # 创建管理员用户
        admin_user = create_admin_user()
        logger.info(f"Admin user ready: {admin_user.api_key}")
        
        # 创建测试用户
        test_users = create_test_users()
        logger.info(f"Created {len(test_users)} test users")
        
        logger.info("Initialization completed successfully!")
        
        # 显示用户信息
        print("\n" + "="*60)
        print("🎉 RECORD FASTAPI SERVICE - 初始化完成")
        print("="*60)
        print("📊 用户账户信息:")
        print(f"   管理员API Key: admin-api-key-change-in-production")
        print(f"   管理员权限级别: 9")
        print(f"   测试用户: test-user-001, test-user-002 (权限级别: 1)")
        print("\n🚀 服务访问地址:")
        print(f"   API文档: http://localhost:7022/docs")
        print(f"   健康检查: http://localhost:7022/health")
        print(f"   服务信息: http://localhost:7022/info")
        print("\n⚠️  安全提醒:")
        print("   生产环境请务必修改默认API Key！")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        exit(1)


if __name__ == "__main__":
    main()
