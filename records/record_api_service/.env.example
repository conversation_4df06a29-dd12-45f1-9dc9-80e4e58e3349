# 数据库配置
DATABASE_URL=mysql+pymysql://records_user:records_password@records-db:3306/vdb_records

# API配置
API_SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_NAME=Record FastAPI Service
APP_VERSION=1.0.0
DEBUG=false

# 服务配置
HOST=0.0.0.0
PORT=7022

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/record-fastapi-service.log

# CORS配置
CORS_ORIGINS=["*"]
CORS_METHODS=["*"]
CORS_HEADERS=["*"]
