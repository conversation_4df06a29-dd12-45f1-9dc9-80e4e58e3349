"""
实用工具函数
"""
import secrets
import string
from typing import Optional
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


def generate_api_key(length: int = 32) -> str:
    """
    生成API密钥
    """
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def calculate_cost(
    prompt_tokens: int,
    completion_tokens: int,
    prompt_price_per_1k: Decimal = Decimal("0.002"),
    completion_price_per_1k: Decimal = Decimal("0.003")
) -> dict:
    """
    计算费用
    
    Args:
        prompt_tokens: 输入token数
        completion_tokens: 输出token数
        prompt_price_per_1k: 每1000个输入token的价格
        completion_price_per_1k: 每1000个输出token的价格
    
    Returns:
        包含费用信息的字典
    """
    try:
        prompt_cost = (Decimal(prompt_tokens) / 1000) * prompt_price_per_1k
        completion_cost = (Decimal(completion_tokens) / 1000) * completion_price_per_1k
        total_cost = prompt_cost + completion_cost
        
        return {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "prompt_cost": round(prompt_cost, 6),
            "completion_cost": round(completion_cost, 6),
            "total_cost": round(total_cost, 6)
        }
    except Exception as e:
        logger.error(f"Error calculating cost: {e}")
        return {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "prompt_cost": Decimal("0.000000"),
            "completion_cost": Decimal("0.000000"),
            "total_cost": Decimal("0.000000")
        }


def validate_url(url: str) -> bool:
    """
    验证URL格式
    """
    if not url:
        return False
    
    # 简单的URL验证
    valid_schemes = ['http://', 'https://', 'minio://', 'file://']
    return any(url.startswith(scheme) for scheme in valid_schemes)


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def truncate_text(text: str, max_length: int = 100) -> str:
    """
    截断文本
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - 3] + "..."


def sanitize_filename(filename: str) -> str:
    """
    清理文件名
    """
    if not filename:
        return "untitled"
    
    # 移除或替换不安全的字符
    unsafe_chars = '<>:"/\\|?*'
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # 限制长度
    if len(filename) > 255:
        filename = filename[:255]
    
    return filename.strip()


def parse_pagination(page: int, size: int, max_size: int = 100) -> tuple:
    """
    解析分页参数
    
    Returns:
        (skip, limit) 元组
    """
    page = max(1, page)
    size = min(max(1, size), max_size)
    skip = (page - 1) * size
    
    return skip, size


def build_response(
    success: bool = True,
    message: str = "操作成功",
    data: Optional[dict] = None,
    error_code: Optional[str] = None
) -> dict:
    """
    构建标准响应格式
    """
    response = {
        "success": success,
        "message": message
    }
    
    if data is not None:
        response["data"] = data
    
    if error_code:
        response["error_code"] = error_code
    
    return response
